import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'
import { ensureUserProfile } from '@/lib/auth/profile-manager'

// GET /api/recipes - Get user's recipes
export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: recipes, error } = await supabase
        .from('recipes')
        .select(`
          id,
          title,
          description,
          prep_time,
          cook_time,
          servings,
          difficulty,
          cuisine_type,
          dietary_tags,
          ingredients,
          instructions,
          image_url,
          source_url,
          rating,
          created_at,
          updated_at
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error

      const response = NextResponse.json({ recipes: recipes || [] })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// POST /api/recipes - Create new recipe
export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const requestData = await request.json()
      const {
        title,
        description,
        prep_time,
        cook_time,
        servings,
        difficulty,
        cuisine_type,
        dietary_tags,
        ingredients,
        instructions,
        image_url,
        source_url
      } = requestData
      
      // Basic validation
      if (!title || typeof title !== 'string' || title.length > 200) {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid title' } 
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: recipe, error } = await supabase
        .from('recipes')
        .insert([
          {
            user_id: user.id,
            title: title.trim(),
            description: description || null,
            prep_time: prep_time || null,
            cook_time: cook_time || null,
            servings: servings || null,
            difficulty: difficulty || null,
            cuisine_type: cuisine_type || null,
            dietary_tags: dietary_tags || [],
            ingredients: ingredients || [],
            instructions: instructions || [],
            image_url: image_url || null,
            source_url: source_url || null,
            rating: 0
          },
        ])
        .select()
        .single()

      if (error) throw error

      const response = NextResponse.json({ recipe })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
