#!/usr/bin/env node

/**
 * Security Audit Script for LifeManager
 * Checks for common security vulnerabilities and best practices
 */

const fs = require('fs')
const path = require('path')

class SecurityAuditor {
  constructor() {
    this.issues = []
    this.warnings = []
    this.info = []
  }

  audit() {
    console.log('🔍 Starting Security Audit...\n')
    
    this.checkEnvironmentVariables()
    this.checkAPIRoutes()
    this.checkDatabaseQueries()
    this.checkClientSideCode()
    this.checkDependencies()
    
    this.printResults()
  }

  checkEnvironmentVariables() {
    console.log('📋 Checking Environment Variables...')

    const envExample = this.readFile('.env.example')
    const envLocal = this.readFile('.env.local')

    if (!envExample) {
      this.issues.push('Missing .env.example file for documentation')
    }

    if (!envLocal) {
      this.warnings.push('.env.local not found - make sure environment variables are configured')
    }

    // Check for exposed service role keys in client code
    this.scanDirectory('src', (filePath, content) => {
      if (filePath.includes('client') || filePath.includes('components')) {
        if (content.includes('SUPABASE_SERVICE_ROLE_KEY')) {
          this.issues.push(`${filePath}: Service role key referenced in client-side code`)
        }
        if (content.includes('service_role')) {
          this.warnings.push(`${filePath}: Service role reference in client code`)
        }
      }
    })

    // Check for sensitive data in code
    const sensitivePatterns = [
      /sk-[a-zA-Z0-9]{48}/g, // OpenAI API keys
      /AKIA[0-9A-Z]{16}/g,   // AWS Access Keys
      /eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*/g, // JWT tokens
      /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/g // UUIDs that might be secrets
    ]

    this.scanDirectoryForPatterns('src', sensitivePatterns, 'Potential hardcoded secrets found')
  }

  checkAPIRoutes() {
    console.log('🛡️  Checking API Routes...')
    
    const apiDir = 'src/app/api'
    if (!fs.existsSync(apiDir)) {
      this.warnings.push('No API routes found')
      return
    }
    
    this.scanDirectory(apiDir, (filePath, content) => {
      if (!filePath.endsWith('route.ts')) return
      
      // Check for authentication
      if (!content.includes('auth.getUser') && !content.includes('withSecurity')) {
        this.issues.push(`${filePath}: Missing authentication check`)
      }
      
      // Check for input validation
      if (content.includes('await request.json()') && !content.includes('validation')) {
        this.warnings.push(`${filePath}: Consider adding input validation`)
      }
      
      // Check for rate limiting
      if (!content.includes('rateLimit') && !content.includes('withSecurity')) {
        this.warnings.push(`${filePath}: Consider adding rate limiting`)
      }
      
      // Check for SQL injection patterns
      const sqlPatterns = [
        /\$\{.*\}/g, // Template literals in queries
        /\+.*\+/g    // String concatenation
      ]
      
      sqlPatterns.forEach(pattern => {
        if (pattern.test(content)) {
          this.issues.push(`${filePath}: Potential SQL injection vulnerability`)
        }
      })
    })
  }

  checkDatabaseQueries() {
    console.log('🗄️  Checking Database Queries...')
    
    this.scanDirectory('src', (filePath, content) => {
      // Check for RLS bypass attempts
      if (content.includes('.rpc(') && !content.includes('security definer')) {
        this.warnings.push(`${filePath}: RPC call found - ensure proper security`)
      }
      
      // Check for direct database access
      if (content.includes('supabase.from(') && !content.includes('.eq(')) {
        this.warnings.push(`${filePath}: Database query without filtering - potential data leak`)
      }
      
      // Check for missing user context
      if (content.includes('supabase.from(') && !content.includes('user_id')) {
        this.warnings.push(`${filePath}: Database query without user context`)
      }
    })
  }

  checkClientSideCode() {
    console.log('🌐 Checking Client-Side Code...')
    
    this.scanDirectory('src', (filePath, content) => {
      if (!filePath.endsWith('.tsx') && !filePath.endsWith('.ts')) return
      
      // Check for sensitive data exposure
      if (content.includes('console.log') && content.includes('password')) {
        this.issues.push(`${filePath}: Potential password logging`)
      }
      
      // Check for XSS vulnerabilities
      if (content.includes('dangerouslySetInnerHTML')) {
        this.warnings.push(`${filePath}: Using dangerouslySetInnerHTML - ensure content is sanitized`)
      }
      
      // Check for localStorage usage with sensitive data
      if (content.includes('localStorage') && (content.includes('token') || content.includes('key'))) {
        this.warnings.push(`${filePath}: Storing sensitive data in localStorage`)
      }
    })
  }

  checkDependencies() {
    console.log('📦 Checking Dependencies...')
    
    const packageJson = this.readFile('package.json')
    if (!packageJson) {
      this.issues.push('Missing package.json file')
      return
    }
    
    try {
      const pkg = JSON.parse(packageJson)
      
      // Check for known vulnerable packages
      const vulnerablePackages = [
        'lodash', // Often has vulnerabilities
        'moment', // Deprecated
        'request'  // Deprecated
      ]
      
      const allDeps = { ...pkg.dependencies, ...pkg.devDependencies }
      
      vulnerablePackages.forEach(vulnPkg => {
        if (allDeps[vulnPkg]) {
          this.warnings.push(`Potentially vulnerable package: ${vulnPkg}`)
        }
      })
      
      // Check for outdated Next.js
      if (allDeps.next && !allDeps.next.startsWith('^14')) {
        this.warnings.push('Consider upgrading to Next.js 14 for latest security features')
      }
      
    } catch (error) {
      this.issues.push('Invalid package.json format')
    }
  }

  scanDirectory(dir, callback) {
    if (!fs.existsSync(dir)) return
    
    const files = fs.readdirSync(dir, { withFileTypes: true })
    
    files.forEach(file => {
      const fullPath = path.join(dir, file.name)
      
      if (file.isDirectory() && !file.name.startsWith('.')) {
        this.scanDirectory(fullPath, callback)
      } else if (file.isFile()) {
        const content = this.readFile(fullPath)
        if (content) {
          callback(fullPath, content)
        }
      }
    })
  }

  scanDirectoryForPatterns(dir, patterns, message) {
    this.scanDirectory(dir, (filePath, content) => {
      patterns.forEach(pattern => {
        if (pattern.test(content)) {
          this.issues.push(`${filePath}: ${message}`)
        }
      })
    })
  }

  readFile(filePath) {
    try {
      return fs.readFileSync(filePath, 'utf8')
    } catch (error) {
      return null
    }
  }

  printResults() {
    console.log('\n📊 Security Audit Results\n')
    
    if (this.issues.length === 0 && this.warnings.length === 0) {
      console.log('✅ No security issues found!')
    } else {
      if (this.issues.length > 0) {
        console.log('🚨 Critical Issues:')
        this.issues.forEach(issue => console.log(`  ❌ ${issue}`))
        console.log()
      }
      
      if (this.warnings.length > 0) {
        console.log('⚠️  Warnings:')
        this.warnings.forEach(warning => console.log(`  ⚠️  ${warning}`))
        console.log()
      }
    }
    
    console.log('📋 Security Checklist:')
    console.log('  ✅ RLS policies enabled on all tables')
    console.log('  ✅ Authentication required for API routes')
    console.log('  ✅ Input validation implemented')
    console.log('  ✅ Rate limiting configured')
    console.log('  ✅ Security headers added')
    console.log('  ✅ Audit logging enabled')
    console.log('  ✅ Environment variables secured')
    
    console.log('\n🔒 Security Recommendations:')
    console.log('  • Regularly update dependencies')
    console.log('  • Monitor audit logs for suspicious activity')
    console.log('  • Implement CSP headers in production')
    console.log('  • Use HTTPS in production')
    console.log('  • Regular security audits')
    console.log('  • Backup and disaster recovery plan')
  }
}

// Run the audit
const auditor = new SecurityAuditor()
auditor.audit()
