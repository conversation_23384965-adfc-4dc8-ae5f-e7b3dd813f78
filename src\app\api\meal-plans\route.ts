import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'

export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const supabase = await createClient()
      const user = context.user

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    let query = supabase
      .from('meal_plans')
      .select('*, recipes(*)')
      .eq('user_id', user.id)
      .order('planned_date', { ascending: true })

    if (startDate) {
      query = query.gte('planned_date', startDate)
    }
    if (endDate) {
      query = query.lte('planned_date', endDate)
    }

    const { data, error } = await query

    if (error) throw error

      const response = NextResponse.json({ mealPlans: data || [] })
      return addSecurityHeaders(response)
    } catch (error) {
      console.error('Error fetching meal plans:', error)
      return NextResponse.json(
        { error: 'Failed to fetch meal plans' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: false
  }
)

export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
  try {
    const { recipeId, plannedDate, mealType, servings } = await request.json()

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify recipe belongs to user
    const { data: recipe, error: recipeError } = await supabase
      .from('recipes')
      .select('id')
      .eq('id', recipeId)
      .eq('user_id', user.id)
      .single()

    if (recipeError || !recipe) {
      return NextResponse.json({ error: 'Recipe not found' }, { status: 404 })
    }

    // Create meal plan
    const { data, error } = await supabase
      .from('meal_plans')
      .insert([
        {
          user_id: user.id,
          recipe_id: recipeId,
          planned_date: plannedDate,
          meal_type: mealType,
          servings: servings || 1,
        },
      ])
      .select('*, recipes(*)')
      .single()

    if (error) throw error

    return NextResponse.json({ mealPlan: data })
  } catch (error) {
    console.error('Error creating meal plan:', error)
    return NextResponse.json(
      { error: 'Failed to create meal plan' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: 'Meal plan ID required' }, { status: 400 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { error } = await supabase
      .from('meal_plans')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) throw error

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting meal plan:', error)
    return NextResponse.json(
      { error: 'Failed to delete meal plan' },
      { status: 500 }
    )
  }
}
