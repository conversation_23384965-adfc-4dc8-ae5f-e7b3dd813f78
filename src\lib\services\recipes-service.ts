export interface Recipe {
  id: string
  title: string
  description?: string
  ingredients: string[]
  instructions: string[]
  prep_time?: number
  cook_time?: number
  servings?: number
  difficulty?: 'easy' | 'medium' | 'hard'
  tags?: string[]
  image_url?: string
  source_url?: string
  created_at: string
  updated_at: string
}

export interface CreateRecipeData {
  title: string
  description?: string
  ingredients: string[]
  instructions: string[]
  prep_time?: number
  cook_time?: number
  servings?: number
  difficulty?: 'easy' | 'medium' | 'hard'
  tags?: string[]
  image_url?: string
  source_url?: string
}

export interface UpdateRecipeData extends Partial<CreateRecipeData> {}

class RecipesService {
  private baseUrl = '/api/recipes'

  async getRecipes(): Promise<Recipe[]> {
    const response = await fetch(this.baseUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch recipes: ${response.statusText}`)
    }

    return response.json()
  }

  async getRecipe(id: string): Promise<Recipe> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch recipe: ${response.statusText}`)
    }

    return response.json()
  }

  async createRecipe(data: CreateRecipeData): Promise<Recipe> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to create recipe: ${response.statusText}`)
    }

    return response.json()
  }

  async updateRecipe(id: string, data: UpdateRecipeData): Promise<Recipe> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to update recipe: ${response.statusText}`)
    }

    return response.json()
  }

  async deleteRecipe(id: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to delete recipe: ${response.statusText}`)
    }
  }

  async importRecipeFromUrl(url: string): Promise<Recipe> {
    const response = await fetch(`${this.baseUrl}/import`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url }),
    })

    if (!response.ok) {
      throw new Error(`Failed to import recipe: ${response.statusText}`)
    }

    return response.json()
  }

  async searchRecipes(query: string): Promise<Recipe[]> {
    const response = await fetch(`${this.baseUrl}/search?q=${encodeURIComponent(query)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to search recipes: ${response.statusText}`)
    }

    return response.json()
  }
}

export const recipesService = new RecipesService()
