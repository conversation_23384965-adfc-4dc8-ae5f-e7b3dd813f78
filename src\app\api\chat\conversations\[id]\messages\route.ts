import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'

// GET /api/chat/conversations/[id]/messages - Get messages for a conversation
export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const { params } = context
      const conversationId = params.id

      if (!conversationId || typeof conversationId !== 'string') {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid conversation ID' } 
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // First verify the conversation belongs to the user
      const { data: conversation, error: fetchError } = await supabase
        .from('chat_conversations')
        .select('id')
        .eq('id', conversationId)
        .eq('user_id', user.id)
        .single()

      if (fetchError || !conversation) {
        return NextResponse.json({ 
          error: { code: 'NOT_FOUND', message: 'Conversation not found' } 
        }, { status: 404 })
      }

      // Get messages for the conversation
      const { data: messages, error } = await supabase
        .from('chat_messages')
        .select('id, role, content, created_at')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true })

      if (error) throw error

      const response = NextResponse.json({ messages: messages || [] })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
