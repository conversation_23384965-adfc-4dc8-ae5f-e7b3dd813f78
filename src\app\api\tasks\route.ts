import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'
import { ensureUserProfile } from '@/lib/auth/profile-manager'

// GET /api/tasks - Get user's tasks
export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: tasks, error } = await supabase
        .from('tasks')
        .select(`
          id,
          title,
          description,
          completed,
          priority,
          due_date,
          category,
          tags,
          created_at,
          updated_at
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error

      const response = NextResponse.json({ tasks: tasks || [] })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// POST /api/tasks - Create new task
export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const requestData = await request.json()
      const {
        title,
        description,
        priority,
        due_date,
        category,
        tags
      } = requestData
      
      // Basic validation
      if (!title || typeof title !== 'string' || title.length > 200) {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid title' } 
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: task, error } = await supabase
        .from('tasks')
        .insert([
          {
            user_id: user.id,
            title: title.trim(),
            description: description || null,
            completed: false,
            priority: priority || 'medium',
            due_date: due_date || null,
            category: category || null,
            tags: tags || []
          },
        ])
        .select()
        .single()

      if (error) throw error

      const response = NextResponse.json({ task })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
