{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/api/recipes/import/route.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\nimport { NextRequest, NextResponse } from 'next/server'\nimport * as cheerio from 'cheerio'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url } = await request.json()\n\n    const supabase = await createClient()\n    const { data: { user } } = await supabase.auth.getUser()\n\n    if (!user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    let recipeData = null\n\n    // First try Spoonacular API for better extraction\n    if (process.env.SPOONACULAR_API_KEY) {\n      try {\n        recipeData = await extractRecipeWithSpoonacular(url)\n      } catch (error) {\n        console.log('Spoonacular extraction failed, falling back to web scraping:', error)\n      }\n    }\n\n    // Fallback to web scraping if Spoonacular fails\n    if (!recipeData) {\n      const response = await fetch(url, {\n        headers: {\n          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n        }\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch recipe page')\n      }\n\n      const html = await response.text()\n      const $ = cheerio.load(html)\n\n      // Try to extract recipe data using JSON-LD structured data\n      $('script[type=\"application/ld+json\"]').each((_, element) => {\n        try {\n          const jsonData = JSON.parse($(element).html() || '')\n          if (jsonData['@type'] === 'Recipe' || (Array.isArray(jsonData) && jsonData.some(item => item['@type'] === 'Recipe'))) {\n            recipeData = Array.isArray(jsonData) ? jsonData.find(item => item['@type'] === 'Recipe') : jsonData\n            return false // Break the loop\n          }\n        } catch (e) {\n          // Continue to next script tag\n        }\n      })\n\n      // If no structured data found, try to extract from common HTML patterns\n      if (!recipeData) {\n        recipeData = extractRecipeFromHTML($)\n      }\n    }\n\n    if (!recipeData) {\n      return NextResponse.json({ error: 'Could not extract recipe data from URL' }, { status: 400 })\n    }\n\n    // Get nutritional information if available\n    let nutritionalInfo = null\n    if (recipeData.spoonacularId && process.env.SPOONACULAR_API_KEY) {\n      try {\n        nutritionalInfo = await getNutritionalInfo(recipeData.spoonacularId)\n      } catch (error) {\n        console.log('Failed to get nutritional info:', error)\n      }\n    }\n\n    // Save recipe to database\n    const { data, error } = await supabase\n      .from('recipes')\n      .insert([\n        {\n          user_id: user.id,\n          title: recipeData.name || 'Imported Recipe',\n          description: recipeData.description || '',\n          ingredients: recipeData.recipeIngredient || [],\n          instructions: recipeData.recipeInstructions || [],\n          prep_time: parseTime(recipeData.prepTime),\n          cook_time: parseTime(recipeData.cookTime),\n          servings: recipeData.recipeYield ? parseInt(recipeData.recipeYield) : null,\n          difficulty: estimateDifficulty(recipeData),\n          cuisine: recipeData.recipeCuisine || null,\n          image_url: recipeData.image || null,\n          source_url: url,\n          tags: extractTags(recipeData),\n          nutritional_info: nutritionalInfo,\n        },\n      ])\n      .select()\n      .single()\n\n    if (error) throw error\n\n    return NextResponse.json({ recipe: data })\n  } catch (error) {\n    console.error('Error importing recipe:', error)\n    return NextResponse.json(\n      { error: 'Failed to import recipe' },\n      { status: 500 }\n    )\n  }\n}\n\nasync function extractRecipeWithSpoonacular(url: string) {\n  const response = await fetch(`https://api.spoonacular.com/recipes/extract?apiKey=${process.env.SPOONACULAR_API_KEY}&url=${encodeURIComponent(url)}`)\n\n  if (!response.ok) {\n    throw new Error('Spoonacular API request failed')\n  }\n\n  const data = await response.json()\n\n  if (!data.title) {\n    throw new Error('No recipe found at URL')\n  }\n\n  return {\n    name: data.title,\n    description: data.summary?.replace(/<[^>]*>/g, '') || '', // Remove HTML tags\n    recipeIngredient: data.extendedIngredients?.map((ing: any) => ing.original) || [],\n    recipeInstructions: data.analyzedInstructions?.[0]?.steps?.map((step: any) => ({ text: step.step })) || [],\n    prepTime: data.preparationMinutes ? `PT${data.preparationMinutes}M` : null,\n    cookTime: data.cookingMinutes ? `PT${data.cookingMinutes}M` : null,\n    recipeYield: data.servings,\n    recipeCuisine: data.cuisines?.[0] || null,\n    image: data.image,\n    spoonacularId: data.id,\n    dishTypes: data.dishTypes || [],\n    diets: data.diets || [],\n    occasions: data.occasions || []\n  }\n}\n\nasync function getNutritionalInfo(recipeId: number) {\n  const response = await fetch(`https://api.spoonacular.com/recipes/${recipeId}/nutritionWidget.json?apiKey=${process.env.SPOONACULAR_API_KEY}`)\n\n  if (!response.ok) {\n    throw new Error('Failed to get nutritional info')\n  }\n\n  const data = await response.json()\n\n  return {\n    calories: data.calories,\n    carbs: data.carbs,\n    fat: data.fat,\n    protein: data.protein,\n    nutrients: data.nutrients || []\n  }\n}\n\nfunction estimateDifficulty(recipeData: any): 'easy' | 'medium' | 'hard' {\n  const instructionCount = recipeData.recipeInstructions?.length || 0\n  const ingredientCount = recipeData.recipeIngredient?.length || 0\n  const totalTime = parseTime(recipeData.prepTime) + parseTime(recipeData.cookTime)\n\n  if (instructionCount <= 5 && ingredientCount <= 8 && totalTime <= 30) {\n    return 'easy'\n  } else if (instructionCount <= 10 && ingredientCount <= 15 && totalTime <= 60) {\n    return 'medium'\n  } else {\n    return 'hard'\n  }\n}\n\nfunction extractTags(recipeData: any): string[] {\n  const tags: string[] = []\n\n  if (recipeData.dishTypes) tags.push(...recipeData.dishTypes)\n  if (recipeData.diets) tags.push(...recipeData.diets)\n  if (recipeData.occasions) tags.push(...recipeData.occasions)\n\n  return [...new Set(tags)] // Remove duplicates\n}\n\nfunction extractRecipeFromHTML($: cheerio.CheerioAPI) {\n  // Try common selectors for recipe data\n  const title = $('h1').first().text().trim() || \n                $('.recipe-title').first().text().trim() ||\n                $('[class*=\"title\"]').first().text().trim()\n\n  const description = $('.recipe-description').first().text().trim() ||\n                     $('[class*=\"description\"]').first().text().trim() ||\n                     $('meta[name=\"description\"]').attr('content')\n\n  // Extract ingredients\n  const ingredients: string[] = []\n  $('.recipe-ingredient, .ingredient, [class*=\"ingredient\"]').each((_, element) => {\n    const text = $(element).text().trim()\n    if (text) ingredients.push(text)\n  })\n\n  // Extract instructions\n  const instructions: string[] = []\n  $('.recipe-instruction, .instruction, [class*=\"instruction\"], .recipe-step, .step').each((_, element) => {\n    const text = $(element).text().trim()\n    if (text) instructions.push(text)\n  })\n\n  // Extract image\n  const image = $('img[class*=\"recipe\"], img[class*=\"hero\"]').first().attr('src') ||\n                $('.recipe-image img').first().attr('src')\n\n  if (!title && ingredients.length === 0) {\n    return null\n  }\n\n  return {\n    name: title,\n    description: description,\n    recipeIngredient: ingredients,\n    recipeInstructions: instructions.map(instruction => ({ text: instruction })),\n    image: image\n  }\n}\n\nfunction parseTime(timeString: string | undefined): number | null {\n  if (!timeString) return null\n  \n  // Parse ISO 8601 duration (PT15M = 15 minutes)\n  const isoMatch = timeString.match(/PT(?:(\\d+)H)?(?:(\\d+)M)?/)\n  if (isoMatch) {\n    const hours = parseInt(isoMatch[1] || '0')\n    const minutes = parseInt(isoMatch[2] || '0')\n    return hours * 60 + minutes\n  }\n  \n  // Parse simple number (assume minutes)\n  const numberMatch = timeString.match(/(\\d+)/)\n  if (numberMatch) {\n    return parseInt(numberMatch[1])\n  }\n  \n  return null\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAEtD,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,IAAI,aAAa;QAEjB,kDAAkD;QAClD,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE;YACnC,IAAI;gBACF,aAAa,MAAM,6BAA6B;YAClD,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,gEAAgE;YAC9E;QACF;QAEA,gDAAgD;QAChD,IAAI,CAAC,YAAY;YACf,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,cAAc;gBAChB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,IAAI,CAAA,GAAA,yJAAA,CAAA,OAAY,AAAD,EAAE;YAEvB,2DAA2D;YAC3D,EAAE,sCAAsC,IAAI,CAAC,CAAC,GAAG;gBAC/C,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC,EAAE,SAAS,IAAI,MAAM;oBACjD,IAAI,QAAQ,CAAC,QAAQ,KAAK,YAAa,MAAM,OAAO,CAAC,aAAa,SAAS,IAAI,CAAC,CAAA,OAAQ,IAAI,CAAC,QAAQ,KAAK,WAAY;wBACpH,aAAa,MAAM,OAAO,CAAC,YAAY,SAAS,IAAI,CAAC,CAAA,OAAQ,IAAI,CAAC,QAAQ,KAAK,YAAY;wBAC3F,OAAO,MAAM,iBAAiB;;oBAChC;gBACF,EAAE,OAAO,GAAG;gBACV,8BAA8B;gBAChC;YACF;YAEA,wEAAwE;YACxE,IAAI,CAAC,YAAY;gBACf,aAAa,sBAAsB;YACrC;QACF;QAEA,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAyC,GAAG;gBAAE,QAAQ;YAAI;QAC9F;QAEA,2CAA2C;QAC3C,IAAI,kBAAkB;QACtB,IAAI,WAAW,aAAa,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE;YAC/D,IAAI;gBACF,kBAAkB,MAAM,mBAAmB,WAAW,aAAa;YACrE,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,mCAAmC;YACjD;QACF;QAEA,0BAA0B;QAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC;YACN;gBACE,SAAS,KAAK,EAAE;gBAChB,OAAO,WAAW,IAAI,IAAI;gBAC1B,aAAa,WAAW,WAAW,IAAI;gBACvC,aAAa,WAAW,gBAAgB,IAAI,EAAE;gBAC9C,cAAc,WAAW,kBAAkB,IAAI,EAAE;gBACjD,WAAW,UAAU,WAAW,QAAQ;gBACxC,WAAW,UAAU,WAAW,QAAQ;gBACxC,UAAU,WAAW,WAAW,GAAG,SAAS,WAAW,WAAW,IAAI;gBACtE,YAAY,mBAAmB;gBAC/B,SAAS,WAAW,aAAa,IAAI;gBACrC,WAAW,WAAW,KAAK,IAAI;gBAC/B,YAAY;gBACZ,MAAM,YAAY;gBAClB,kBAAkB;YACpB;SACD,EACA,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QAEjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,QAAQ;QAAK;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,6BAA6B,GAAW;IACrD,MAAM,WAAW,MAAM,MAAM,CAAC,mDAAmD,EAAE,QAAQ,GAAG,CAAC,mBAAmB,CAAC,KAAK,EAAE,mBAAmB,MAAM;IAEnJ,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,CAAC,KAAK,KAAK,EAAE;QACf,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QACL,MAAM,KAAK,KAAK;QAChB,aAAa,KAAK,OAAO,EAAE,QAAQ,YAAY,OAAO;QACtD,kBAAkB,KAAK,mBAAmB,EAAE,IAAI,CAAC,MAAa,IAAI,QAAQ,KAAK,EAAE;QACjF,oBAAoB,KAAK,oBAAoB,EAAE,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,OAAc,CAAC;gBAAE,MAAM,KAAK,IAAI;YAAC,CAAC,MAAM,EAAE;QAC1G,UAAU,KAAK,kBAAkB,GAAG,CAAC,EAAE,EAAE,KAAK,kBAAkB,CAAC,CAAC,CAAC,GAAG;QACtE,UAAU,KAAK,cAAc,GAAG,CAAC,EAAE,EAAE,KAAK,cAAc,CAAC,CAAC,CAAC,GAAG;QAC9D,aAAa,KAAK,QAAQ;QAC1B,eAAe,KAAK,QAAQ,EAAE,CAAC,EAAE,IAAI;QACrC,OAAO,KAAK,KAAK;QACjB,eAAe,KAAK,EAAE;QACtB,WAAW,KAAK,SAAS,IAAI,EAAE;QAC/B,OAAO,KAAK,KAAK,IAAI,EAAE;QACvB,WAAW,KAAK,SAAS,IAAI,EAAE;IACjC;AACF;AAEA,eAAe,mBAAmB,QAAgB;IAChD,MAAM,WAAW,MAAM,MAAM,CAAC,oCAAoC,EAAE,SAAS,6BAA6B,EAAE,QAAQ,GAAG,CAAC,mBAAmB,EAAE;IAE7I,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,OAAO;QACL,UAAU,KAAK,QAAQ;QACvB,OAAO,KAAK,KAAK;QACjB,KAAK,KAAK,GAAG;QACb,SAAS,KAAK,OAAO;QACrB,WAAW,KAAK,SAAS,IAAI,EAAE;IACjC;AACF;AAEA,SAAS,mBAAmB,UAAe;IACzC,MAAM,mBAAmB,WAAW,kBAAkB,EAAE,UAAU;IAClE,MAAM,kBAAkB,WAAW,gBAAgB,EAAE,UAAU;IAC/D,MAAM,YAAY,UAAU,WAAW,QAAQ,IAAI,UAAU,WAAW,QAAQ;IAEhF,IAAI,oBAAoB,KAAK,mBAAmB,KAAK,aAAa,IAAI;QACpE,OAAO;IACT,OAAO,IAAI,oBAAoB,MAAM,mBAAmB,MAAM,aAAa,IAAI;QAC7E,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEA,SAAS,YAAY,UAAe;IAClC,MAAM,OAAiB,EAAE;IAEzB,IAAI,WAAW,SAAS,EAAE,KAAK,IAAI,IAAI,WAAW,SAAS;IAC3D,IAAI,WAAW,KAAK,EAAE,KAAK,IAAI,IAAI,WAAW,KAAK;IACnD,IAAI,WAAW,SAAS,EAAE,KAAK,IAAI,IAAI,WAAW,SAAS;IAE3D,OAAO;WAAI,IAAI,IAAI;KAAM,CAAC,oBAAoB;;AAChD;AAEA,SAAS,sBAAsB,CAAqB;IAClD,uCAAuC;IACvC,MAAM,QAAQ,EAAE,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI,MAC3B,EAAE,iBAAiB,KAAK,GAAG,IAAI,GAAG,IAAI,MACtC,EAAE,oBAAoB,KAAK,GAAG,IAAI,GAAG,IAAI;IAEvD,MAAM,cAAc,EAAE,uBAAuB,KAAK,GAAG,IAAI,GAAG,IAAI,MAC7C,EAAE,0BAA0B,KAAK,GAAG,IAAI,GAAG,IAAI,MAC/C,EAAE,4BAA4B,IAAI,CAAC;IAEtD,sBAAsB;IACtB,MAAM,cAAwB,EAAE;IAChC,EAAE,0DAA0D,IAAI,CAAC,CAAC,GAAG;QACnE,MAAM,OAAO,EAAE,SAAS,IAAI,GAAG,IAAI;QACnC,IAAI,MAAM,YAAY,IAAI,CAAC;IAC7B;IAEA,uBAAuB;IACvB,MAAM,eAAyB,EAAE;IACjC,EAAE,kFAAkF,IAAI,CAAC,CAAC,GAAG;QAC3F,MAAM,OAAO,EAAE,SAAS,IAAI,GAAG,IAAI;QACnC,IAAI,MAAM,aAAa,IAAI,CAAC;IAC9B;IAEA,gBAAgB;IAChB,MAAM,QAAQ,EAAE,4CAA4C,KAAK,GAAG,IAAI,CAAC,UAC3D,EAAE,qBAAqB,KAAK,GAAG,IAAI,CAAC;IAElD,IAAI,CAAC,SAAS,YAAY,MAAM,KAAK,GAAG;QACtC,OAAO;IACT;IAEA,OAAO;QACL,MAAM;QACN,aAAa;QACb,kBAAkB;QAClB,oBAAoB,aAAa,GAAG,CAAC,CAAA,cAAe,CAAC;gBAAE,MAAM;YAAY,CAAC;QAC1E,OAAO;IACT;AACF;AAEA,SAAS,UAAU,UAA8B;IAC/C,IAAI,CAAC,YAAY,OAAO;IAExB,+CAA+C;IAC/C,MAAM,WAAW,WAAW,KAAK,CAAC;IAClC,IAAI,UAAU;QACZ,MAAM,QAAQ,SAAS,QAAQ,CAAC,EAAE,IAAI;QACtC,MAAM,UAAU,SAAS,QAAQ,CAAC,EAAE,IAAI;QACxC,OAAO,QAAQ,KAAK;IACtB;IAEA,uCAAuC;IACvC,MAAM,cAAc,WAAW,KAAK,CAAC;IACrC,IAAI,aAAa;QACf,OAAO,SAAS,WAAW,CAAC,EAAE;IAChC;IAEA,OAAO;AACT", "debugId": null}}]}