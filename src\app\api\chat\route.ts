import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders, sanitizeString } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'
import { ensureUserProfile } from '@/lib/auth/profile-manager'
import OpenAI from 'openai'

const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}) : null

export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const requestData = await request.json()
      const { message, conversationId } = requestData

      // Manual validation
      if (!message || typeof message !== 'string' || message.trim().length === 0) {
        return NextResponse.json({
          error: { code: 'VALIDATION_ERROR', message: 'Message is required and cannot be empty' }
        }, { status: 400 })
      }

      if (message.length > 2000) {
        return NextResponse.json({
          error: { code: 'VALIDATION_ERROR', message: 'Message is too long (max 2000 characters)' }
        }, { status: 400 })
      }

      if (conversationId && typeof conversationId !== 'string') {
        return NextResponse.json({
          error: { code: 'VALIDATION_ERROR', message: 'Invalid conversation ID' }
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      // Sanitize input
      const sanitizedMessage = sanitizeString(message)

    // Save user message
    const { error: userMessageError } = await supabase
      .from('chat_messages')
      .insert([
        {
          conversation_id: conversationId,
          role: 'user',
          content: message,
        },
      ])

    if (userMessageError) throw userMessageError

    // Get conversation history for context
    const { data: conversationHistory } = await supabase
      .from('chat_messages')
      .select('role, content')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true })
      .limit(20) // Last 20 messages for context

    // Get user context data
    const userContext = await getUserContext(supabase, user.id)

    // Generate AI response using OpenAI
    const aiResponse = await generateAIResponse(message, conversationHistory || [], userContext)

    // Save AI response
    const { error: aiMessageError } = await supabase
      .from('chat_messages')
      .insert([
        {
          conversation_id: conversationId,
          role: 'assistant',
          content: aiResponse,
        },
      ])

    if (aiMessageError) throw aiMessageError

      const response = NextResponse.json({ response: aiResponse })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.chat,
    auditLog: true
  }
)

async function getUserContext(supabase: any, userId: string) {
  try {
    // Get recent tasks
    const { data: tasks } = await supabase
      .from('tasks')
      .select('title, completed, priority, due_date')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10)

    // Get recent budget data
    const { data: budgetCategories } = await supabase
      .from('budget_categories')
      .select('name, type, budget_limit')
      .eq('user_id', userId)

    const { data: recentTransactions } = await supabase
      .from('transactions')
      .select('amount, description, type, date')
      .eq('user_id', userId)
      .order('date', { ascending: false })
      .limit(5)

    // Get shopping lists
    const { data: shoppingLists } = await supabase
      .from('shopping_lists')
      .select('name, store_name')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5)

    // Get recent recipes
    const { data: recipes } = await supabase
      .from('recipes')
      .select('title, cuisine, difficulty')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5)

    return {
      tasks: tasks || [],
      budgetCategories: budgetCategories || [],
      recentTransactions: recentTransactions || [],
      shoppingLists: shoppingLists || [],
      recipes: recipes || []
    }
  } catch (error) {
    console.error('Error fetching user context:', error)
    return {
      tasks: [],
      budgetCategories: [],
      recentTransactions: [],
      shoppingLists: [],
      recipes: []
    }
  }
}

async function generateAIResponse(
  message: string,
  conversationHistory: Array<{role: string, content: string}>,
  userContext: any
): Promise<string> {
  try {
    const systemPrompt = `You are a helpful AI assistant for LifeManager, a personal life management application. You help users with:

1. Task Management - Creating, organizing, and tracking tasks
2. Budget Management - Tracking income, expenses, and budget categories
3. Shopping Lists - Creating smart shopping lists with price tracking
4. Recipe Management - Saving and organizing recipes

User Context:
- Recent Tasks: ${JSON.stringify(userContext.tasks)}
- Budget Categories: ${JSON.stringify(userContext.budgetCategories)}
- Recent Transactions: ${JSON.stringify(userContext.recentTransactions)}
- Shopping Lists: ${JSON.stringify(userContext.shoppingLists)}
- Recent Recipes: ${JSON.stringify(userContext.recipes)}

Guidelines:
- Be helpful, friendly, and concise
- Provide specific, actionable advice based on the user's data
- Suggest improvements and optimizations
- If the user asks about data they don't have, guide them to create it
- Make smart recommendations based on patterns in their data
- Always be encouraging and supportive

Respond naturally and conversationally. Use the user's actual data to provide personalized insights and recommendations.`

    const messages = [
      { role: 'system', content: systemPrompt },
      ...conversationHistory.slice(-10), // Last 10 messages for context
      { role: 'user', content: message }
    ]

    if (!openai) {
      console.warn('OpenAI API key not configured, using fallback response')
      return generateFallbackResponse(message, userContext)
    }

    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: messages as any,
      max_tokens: 500,
      temperature: 0.7,
    })

    return completion.choices[0]?.message?.content || 'I apologize, but I encountered an issue generating a response. Please try again.'
  } catch (error) {
    console.error('Error generating AI response:', error)

    // Fallback to rule-based response if OpenAI fails
    return generateFallbackResponse(message, userContext)
  }
}

function generateFallbackResponse(message: string, userContext: any): string {
  const lowerMessage = message.toLowerCase()

  // Greeting responses
  if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
    return "Hello! I'm your LifeManager AI assistant. I can help you with tasks, budgeting, shopping lists, and recipes. What would you like to work on today?"
  }

  // Task-related queries
  if (lowerMessage.includes('task') || lowerMessage.includes('todo')) {
    const incompleteTasks = userContext.tasks?.filter((t: any) => !t.completed)?.length || 0
    if (incompleteTasks > 0) {
      return `I can help you manage your tasks! You currently have ${incompleteTasks} incomplete tasks. Would you like me to help you prioritize them or create a new task?`
    } else {
      return "Great job staying on top of your tasks! Would you like me to help you create a new task or set up a task management system?"
    }
  }

  // Budget-related queries
  if (lowerMessage.includes('budget') || lowerMessage.includes('money') || lowerMessage.includes('expense')) {
    const totalCategories = userContext.budgetCategories?.length || 0
    return `I can assist you with budget management! You have ${totalCategories} budget categories set up. Would you like help analyzing your spending or setting up new budget limits?`
  }

  // Shopping-related queries
  if (lowerMessage.includes('shopping') || lowerMessage.includes('grocery') || lowerMessage.includes('buy')) {
    const activeLists = userContext.shoppingLists?.length || 0
    return `I can help you organize your shopping! You have ${activeLists} shopping lists. Would you like me to help you create a new list or optimize your existing ones?`
  }

  // Recipe-related queries
  if (lowerMessage.includes('recipe') || lowerMessage.includes('cook') || lowerMessage.includes('meal')) {
    const recipeCount = userContext.recipes?.length || 0
    return `I can help you with recipes and meal planning! You have ${recipeCount} recipes saved. Would you like help planning meals or finding new recipes?`
  }

  // Help queries
  if (lowerMessage.includes('help') || lowerMessage.includes('what can you do')) {
    return "I'm your personal LifeManager assistant! I can help you with:\n\n• 📋 Task management and productivity\n• 💰 Budget tracking and financial planning\n• 🛒 Smart shopping lists\n• 🍳 Recipe organization and meal planning\n\nJust ask me about any of these areas, and I'll provide personalized assistance based on your data!"
  }

  // Default response
  return "I'm here to help you manage your life more effectively! I can assist with tasks, budgeting, shopping lists, and recipes. What specific area would you like help with?"
}
