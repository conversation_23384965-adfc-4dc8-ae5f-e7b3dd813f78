import { NextResponse } from 'next/server'

// Security headers helper
export function addSecurityHeaders(response: NextResponse): NextResponse {
  // Security headers
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;"
  )
  
  // Remove server information
  response.headers.delete('Server')
  response.headers.delete('X-Powered-By')
  
  return response
}

// Input sanitization helpers
export function sanitizeString(input: string): string {
  if (!input || typeof input !== 'string') return ''
  
  // Remove potentially dangerous characters
  return input
    .replace(/[<>]/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim()
    .slice(0, 10000) // Limit length
}

export function sanitizeNumber(input: any): number | null {
  const num = Number(input)
  if (isNaN(num) || !isFinite(num)) return null
  return num
}

export function sanitizeArray(input: any): any[] {
  if (!Array.isArray(input)) return []
  return input.slice(0, 100) // Limit array size
}

export function sanitizeObject(input: any, allowedKeys: string[]): any {
  if (typeof input !== 'object' || input === null) return {}
  
  const sanitized: any = {}
  for (const key of allowedKeys) {
    if (key in input) {
      sanitized[key] = input[key]
    }
  }
  return sanitized
}
