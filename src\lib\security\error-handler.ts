import { NextResponse } from 'next/server'

export interface SecureError {
  code: string
  message: string
  statusCode: number
}

// Define secure error mappings
const ERROR_MAPPINGS: Record<string, SecureError> = {
  // Authentication errors
  'PGRST301': { code: 'AUTH_REQUIRED', message: 'Authentication required', statusCode: 401 },
  'PGRST302': { code: 'FORBIDDEN', message: 'Access denied', statusCode: 403 },
  
  // Database constraint errors
  '23503': { code: 'INVALID_REFERENCE', message: 'Invalid data reference', statusCode: 400 },
  '23505': { code: 'DUPLICATE_ENTRY', message: 'Duplicate entry not allowed', statusCode: 409 },
  '23514': { code: 'VALIDATION_ERROR', message: 'Data validation failed', statusCode: 400 },
  
  // Rate limiting
  'RATE_LIMIT': { code: 'RATE_LIMIT', message: 'Too many requests', statusCode: 429 },
  
  // Validation errors
  'VALIDATION_ERROR': { code: 'VALIDATION_ERROR', message: 'Invalid input data', statusCode: 400 },
  
  // Generic errors
  'INTERNAL_ERROR': { code: 'INTERNAL_ERROR', message: 'An unexpected error occurred', statusCode: 500 },
}

export function handleSecureError(error: any): NextResponse {
  // Log the full error for debugging (server-side only)
  console.error('Secure Error Handler:', {
    message: error.message,
    code: error.code,
    details: error.details,
    timestamp: new Date().toISOString(),
    stack: error.stack
  })

  // Determine the error type
  let secureError: SecureError

  if (error.code && ERROR_MAPPINGS[error.code]) {
    secureError = ERROR_MAPPINGS[error.code]
  } else if (error.message?.includes('rate limit')) {
    secureError = ERROR_MAPPINGS['RATE_LIMIT']
  } else if (error.message?.includes('validation')) {
    secureError = ERROR_MAPPINGS['VALIDATION_ERROR']
  } else {
    secureError = ERROR_MAPPINGS['INTERNAL_ERROR']
  }

  // Return sanitized error response
  return NextResponse.json(
    {
      error: {
        code: secureError.code,
        message: secureError.message
      }
    },
    { status: secureError.statusCode }
  )
}

export function createValidationError(message: string): Error {
  const error = new Error(message)
  error.name = 'VALIDATION_ERROR'
  return error
}

export function createAuthError(message: string = 'Authentication required'): Error {
  const error = new Error(message)
  error.name = 'AUTH_REQUIRED'
  return error
}

export function createForbiddenError(message: string = 'Access denied'): Error {
  const error = new Error(message)
  error.name = 'FORBIDDEN'
  return error
}
