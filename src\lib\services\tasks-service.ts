// Client-side service for task operations - all calls go through Next.js API

export interface Task {
  id: string
  title: string
  description?: string
  completed: boolean
  priority: 'low' | 'medium' | 'high'
  due_date?: string
  category?: string
  tags: string[]
  created_at: string
  updated_at: string
}

class TasksService {
  private async makeRequest(url: string, options: RequestInit = {}) {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      const errorMessage = errorData.error?.message || errorData.error || 'Request failed'
      throw new Error(errorMessage)
    }

    return response.json()
  }

  // Get all tasks for the current user
  async getTasks(): Promise<Task[]> {
    const data = await this.makeRequest('/api/tasks')
    return data.tasks
  }

  // Create a new task
  async createTask(taskData: Partial<Task>): Promise<Task> {
    const data = await this.makeRequest('/api/tasks', {
      method: 'POST',
      body: JSON.stringify(taskData),
    })
    return data.task
  }

  // Update a task
  async updateTask(taskId: string, updates: Partial<Task>): Promise<Task> {
    const data = await this.makeRequest(`/api/tasks/${taskId}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    })
    return data.task
  }

  // Delete a task
  async deleteTask(taskId: string): Promise<void> {
    await this.makeRequest(`/api/tasks/${taskId}`, {
      method: 'DELETE',
    })
  }

  // Toggle task completion
  async toggleTask(taskId: string, completed: boolean): Promise<Task> {
    return this.updateTask(taskId, { completed })
  }
}

// Export a singleton instance
export const tasksService = new TasksService()
