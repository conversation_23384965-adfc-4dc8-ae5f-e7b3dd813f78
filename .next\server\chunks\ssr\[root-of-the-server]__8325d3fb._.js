module.exports = {

"[project]/node_modules/@capacitor/splash-screen/dist/esm/web.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@capacitor_splash-screen_dist_esm_web_fa1b1acf.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@capacitor/splash-screen/dist/esm/web.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@capacitor/haptics/dist/esm/web.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@capacitor_haptics_dist_esm_web_16aba8ac.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@capacitor/haptics/dist/esm/web.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@capacitor/toast/dist/esm/web.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@capacitor_toast_dist_esm_web_dd03c717.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@capacitor/toast/dist/esm/web.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@capacitor/share/dist/esm/web.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@capacitor_share_dist_esm_web_20588e45.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@capacitor/share/dist/esm/web.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@capacitor/network/dist/esm/web.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@capacitor_network_dist_esm_web_5d47fcf0.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@capacitor/network/dist/esm/web.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@capacitor/device/dist/esm/web.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@capacitor_device_dist_esm_web_2a546c88.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@capacitor/device/dist/esm/web.js [app-ssr] (ecmascript)");
    });
});
}}),
"[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_59fa4ecd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@supabase/node-fetch/lib/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@supabase/node-fetch/lib/index.js [app-ssr] (ecmascript)");
    });
});
}}),

};