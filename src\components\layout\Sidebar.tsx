'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { useSidebar } from '@/components/layout/SidebarContext'
import {
  HomeIcon,
  CheckSquareIcon,
  DollarSignIcon,
  ShoppingCartIcon,
  MessageCircleIcon,
  ChefHatIcon,
  LogOutIcon,
  MenuIcon,
  XIcon,
  UserIcon,
  SettingsIcon,
  BellIcon,
  SparklesIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  TrendingUpIcon,
  TargetIcon,
  CalendarIcon,
} from 'lucide-react'

const getNavigation = (stats: any) => [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: HomeIcon,
    color: 'text-blue-600',
    badge: null,
    description: 'Overview & Analytics'
  },
  {
    name: 'Tasks',
    href: '/tasks',
    icon: CheckSquareIcon,
    color: 'text-green-600',
    badge: stats.tasksToday,
    description: 'Manage your tasks'
  },
  {
    name: 'Budget',
    href: '/budget',
    icon: DollarSignIcon,
    color: 'text-emerald-600',
    badge: stats.budgetAlert ? '!' : null,
    description: 'Track expenses'
  },
  {
    name: 'Shopping Lists',
    href: '/shopping',
    icon: ShoppingCartIcon,
    color: 'text-purple-600',
    badge: null,
    description: 'Shopping & Lists'
  },
  {
    name: 'AI Chat',
    href: '/chat',
    icon: MessageCircleIcon,
    color: 'text-indigo-600',
    badge: null,
    description: 'AI Assistant'
  },
  {
    name: 'Recipes',
    href: '/recipes',
    icon: ChefHatIcon,
    color: 'text-orange-600',
    badge: null,
    description: 'Recipe Collection'
  },
  {
    name: 'Meal Plans',
    href: '/meal-plans',
    icon: CalendarIcon,
    color: 'text-pink-600',
    badge: null,
    description: 'Weekly Meal Planning'
  },
]

export default function Sidebar() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { isCollapsed, toggleCollapsed } = useSidebar()
  const [user, setUser] = useState<any>(null)
  const [notifications, setNotifications] = useState(3)
  const [quickStats, setQuickStats] = useState({
    tasksToday: 5,
    budgetAlert: true,
    upcomingDeadlines: 2
  })
  const pathname = usePathname()
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
    }
    getUser()
  }, [])

  const handleLogout = async () => {
    await supabase.auth.signOut()
    router.push('/login')
    router.refresh()
  }

  const getUserInitials = (email: string) => {
    return email.split('@')[0].slice(0, 2).toUpperCase()
  }

  const navigation = getNavigation(quickStats)

  return (
    <>
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity" onClick={() => setSidebarOpen(false)} />
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white shadow-xl">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <button
              type="button"
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white hover:bg-gray-600 transition-colors"
              onClick={() => setSidebarOpen(false)}
            >
              <XIcon className="h-6 w-6 text-white" />
            </button>
          </div>

          {/* Header with logo and user */}
          <div className="flex-shrink-0 px-4 py-6 bg-gradient-to-r from-blue-600 to-indigo-600">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <SparklesIcon className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-xl font-bold text-white">LifeManager</h1>
              </div>
            </div>
            {user && (
              <div className="mt-4 flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {getUserInitials(user.email)}
                    </span>
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-white truncate">
                    {user.email}
                  </p>
                  <p className="text-xs text-blue-100">
                    Welcome back!
                  </p>
                </div>
              </div>
            )}
          </div>

          <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
            <nav className="px-3 space-y-1" role="navigation" aria-label="Main navigation">
              {navigation.map((item) => {
                const isActive = pathname === item.href
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={() => setSidebarOpen(false)}
                    className={`group flex items-center px-3 py-3 text-base font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                      isActive
                        ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-600'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                    aria-current={isActive ? 'page' : undefined}
                  >
                    <item.icon
                      className={`mr-4 h-6 w-6 transition-colors ${
                        isActive ? 'text-blue-600' : `${item.color} group-hover:${item.color}`
                      }`}
                      aria-hidden="true"
                    />
                    {item.name}
                  </Link>
                )
              })}
            </nav>
          </div>

          <div className="flex-shrink-0 border-t border-gray-200 p-4">
            <button
              onClick={handleLogout}
              className="w-full flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
            >
              <LogOutIcon className="mr-3 h-5 w-5" />
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Desktop sidebar */}
      <motion.div
        className="hidden md:flex md:flex-col md:fixed md:inset-y-0 z-30"
        animate={{ width: isCollapsed ? 80 : 280 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        <div className="flex-1 flex flex-col min-h-0 bg-white shadow-xl border-r border-gray-200">
          {/* Header with logo and user */}
          <div className="flex-shrink-0 px-4 py-6 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm"></div>
            <div className="relative">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <motion.div
                    animate={{ scale: isCollapsed ? 1.2 : 1 }}
                    transition={{ duration: 0.3 }}
                    className="flex-shrink-0"
                  >
                    <SparklesIcon className="h-8 w-8 text-white" />
                  </motion.div>
                  <AnimatePresence>
                    {!isCollapsed && (
                      <motion.h1
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ duration: 0.2 }}
                        className="text-xl font-bold text-white"
                      >
                        LifeManager
                      </motion.h1>
                    )}
                  </AnimatePresence>
                </div>

                <motion.button
                  onClick={toggleCollapsed}
                  className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {isCollapsed ? (
                    <ChevronRightIcon className="h-4 w-4 text-white" />
                  ) : (
                    <ChevronLeftIcon className="h-4 w-4 text-white" />
                  )}
                </motion.button>
              </div>

              <AnimatePresence>
                {user && !isCollapsed && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                    className="mt-4 flex items-center space-x-3"
                  >
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center ring-2 ring-white/30">
                        <span className="text-sm font-medium text-white">
                          {getUserInitials(user.email)}
                        </span>
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-white truncate">
                        {user.email}
                      </p>
                      <p className="text-xs text-blue-100">
                        Welcome back! 👋
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Quick Stats */}
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="px-4 py-3 bg-gray-50 border-b border-gray-200"
              >
                <div className="grid grid-cols-3 gap-2 text-center">
                  <div className="p-2 bg-white rounded-lg shadow-sm">
                    <div className="text-lg font-bold text-blue-600">{quickStats.tasksToday}</div>
                    <div className="text-xs text-gray-600">Today</div>
                  </div>
                  <div className="p-2 bg-white rounded-lg shadow-sm">
                    <div className="text-lg font-bold text-green-600">85%</div>
                    <div className="text-xs text-gray-600">Complete</div>
                  </div>
                  <div className="p-2 bg-white rounded-lg shadow-sm">
                    <div className="text-lg font-bold text-orange-600">{quickStats.upcomingDeadlines}</div>
                    <div className="text-xs text-gray-600">Due</div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
            <nav className="flex-1 px-3 space-y-2" role="navigation" aria-label="Main navigation">
              {navigation.map((item, index) => {
                const isActive = pathname === item.href
                return (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <Link
                      href={item.href}
                      className={`group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 relative overflow-hidden ${
                        isActive
                          ? 'bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 shadow-md border border-blue-200'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'
                      }`}
                      aria-current={isActive ? 'page' : undefined}
                      title={isCollapsed ? item.name : undefined}
                    >
                      {isActive && (
                        <motion.div
                          layoutId="activeTab"
                          className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-indigo-500/10"
                          transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                        />
                      )}

                      <div className="relative flex items-center w-full">
                        <motion.div
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                          className="flex-shrink-0"
                        >
                          <item.icon
                            className={`h-5 w-5 transition-colors ${
                              isActive ? 'text-blue-600' : `${item.color} group-hover:${item.color}`
                            }`}
                            aria-hidden="true"
                          />
                        </motion.div>

                        <AnimatePresence>
                          {!isCollapsed && (
                            <motion.div
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              exit={{ opacity: 0, x: -10 }}
                              transition={{ duration: 0.2 }}
                              className="ml-3 flex-1 flex items-center justify-between"
                            >
                              <div>
                                <div className="font-medium">{item.name}</div>
                                <div className="text-xs text-gray-500">{item.description}</div>
                              </div>

                              {item.badge && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className={`ml-2 px-2 py-1 text-xs font-bold rounded-full ${
                                    item.badge === '!'
                                      ? 'bg-red-100 text-red-600'
                                      : 'bg-blue-100 text-blue-600'
                                  }`}
                                >
                                  {item.badge}
                                </motion.div>
                              )}
                            </motion.div>
                          )}
                        </AnimatePresence>

                        {isCollapsed && item.badge && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"
                          />
                        )}
                      </div>
                    </Link>
                  </motion.div>
                )
              })}
            </nav>
          </div>

          <div className="flex-shrink-0 border-t border-gray-200 p-4">
            <motion.button
              onClick={handleLogout}
              className={`w-full flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors ${
                isCollapsed ? 'justify-center' : ''
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              title={isCollapsed ? 'Logout' : undefined}
            >
              <LogOutIcon className="h-5 w-5" />
              <AnimatePresence>
                {!isCollapsed && (
                  <motion.span
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.2 }}
                    className="ml-3"
                  >
                    Logout
                  </motion.span>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* Mobile menu button */}
      <div className="md:hidden">
        <div className="flex items-center justify-between bg-white border-b border-gray-200 px-4 py-3 shadow-sm sticky top-0 z-40">
          <div className="flex items-center space-x-2">
            <SparklesIcon className="h-6 w-6 text-blue-600" />
            <h1 className="text-lg font-semibold text-gray-900 truncate">LifeManager</h1>
          </div>
          <div className="flex items-center space-x-1">
            {user && (
              <div className="flex items-center space-x-2 mr-2">
                <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-xs font-medium text-blue-700">
                    {getUserInitials(user.email)}
                  </span>
                </div>
              </div>
            )}
            <button
              type="button"
              className="touch-target inline-flex items-center justify-center p-2 rounded-lg text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors"
            >
              <BellIcon className="h-5 w-5" />
            </button>
            <button
              type="button"
              className="touch-target inline-flex items-center justify-center p-2 rounded-lg text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors"
              onClick={() => setSidebarOpen(true)}
            >
              <MenuIcon className="h-6 w-6" />
            </button>
          </div>
        </div>
      </div>
    </>
  )
}
