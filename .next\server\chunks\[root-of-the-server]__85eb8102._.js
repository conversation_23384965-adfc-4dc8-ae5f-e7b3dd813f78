module.exports = {

"[project]/.next-internal/server/app/api/chat/conversations/[id]/messages/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/supabase/server.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
;
;
async function createClient() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServerClient"])(("TURBOPACK compile-time value", "https://ocyjxnddxuhhlnguybre.supabase.co"), ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9jeWp4bmRkeHVoaGxuZ3V5YnJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NDY2ODYsImV4cCI6MjA2NzUyMjY4Nn0.LQJjfsT5lKAnfD7dvWMtKMjgLiFo-vjcone8yi-Gm40"), {
        cookies: {
            getAll () {
                return cookieStore.getAll();
            },
            setAll (cookiesToSet) {
                try {
                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
}}),
"[project]/src/lib/security/rate-limit.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Simple in-memory rate limiting
// In production, you'd want to use Redis or a similar solution
__turbopack_context__.s({
    "rateLimit": (()=>rateLimit),
    "rateLimitByIP": (()=>rateLimitByIP),
    "rateLimitByUser": (()=>rateLimitByUser),
    "rateLimitConfigs": (()=>rateLimitConfigs)
});
const rateLimitStore = new Map();
async function rateLimit(identifier, requests = 100, windowMs = '1h') {
    const windowMilliseconds = parseTimeWindow(windowMs);
    const now = Date.now();
    const key = `${identifier}:${Math.floor(now / windowMilliseconds)}`;
    const entry = rateLimitStore.get(key);
    if (!entry) {
        rateLimitStore.set(key, {
            count: 1,
            resetTime: now + windowMilliseconds
        });
        // Clean up old entries
        cleanupOldEntries(now);
        return {
            success: true
        };
    }
    if (entry.count >= requests) {
        const retryAfter = Math.ceil((entry.resetTime - now) / 1000);
        return {
            success: false,
            retryAfter: retryAfter > 0 ? retryAfter : 1
        };
    }
    entry.count++;
    return {
        success: true
    };
}
function parseTimeWindow(window) {
    const match = window.match(/^(\d+)([smhd])$/);
    if (!match) return 3600000 // Default to 1 hour
    ;
    const value = parseInt(match[1]);
    const unit = match[2];
    switch(unit){
        case 's':
            return value * 1000;
        case 'm':
            return value * 60 * 1000;
        case 'h':
            return value * 60 * 60 * 1000;
        case 'd':
            return value * 24 * 60 * 60 * 1000;
        default:
            return 3600000;
    }
}
function cleanupOldEntries(now) {
    // Clean up entries older than 24 hours
    const cutoff = now - 24 * 60 * 60 * 1000;
    for (const [key, entry] of rateLimitStore.entries()){
        if (entry.resetTime < cutoff) {
            rateLimitStore.delete(key);
        }
    }
}
const rateLimitConfigs = {
    // Authentication endpoints
    auth: {
        requests: 5,
        window: '15m'
    },
    // API endpoints
    api: {
        requests: 100,
        window: '1h'
    },
    // File upload endpoints
    upload: {
        requests: 10,
        window: '1h'
    },
    // Recipe import (external API calls)
    import: {
        requests: 20,
        window: '1h'
    },
    // Chat/AI endpoints
    chat: {
        requests: 50,
        window: '1h'
    },
    // Search endpoints
    search: {
        requests: 200,
        window: '1h'
    }
};
async function rateLimitByIP(request, config = rateLimitConfigs.api) {
    const ip = getClientIP(request);
    return rateLimit(`ip:${ip}`, config.requests, config.window);
}
async function rateLimitByUser(userId, config = rateLimitConfigs.api) {
    return rateLimit(`user:${userId}`, config.requests, config.window);
}
function getClientIP(request) {
    // Try to get IP from various headers
    const forwarded = request.headers.get('x-forwarded-for');
    if (forwarded) {
        return forwarded.split(',')[0].trim();
    }
    const realIP = request.headers.get('x-real-ip');
    if (realIP) {
        return realIP;
    }
    const cfConnectingIP = request.headers.get('cf-connecting-ip');
    if (cfConnectingIP) {
        return cfConnectingIP;
    }
    return 'unknown';
}
}}),
"[project]/src/lib/security/validation.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "patterns": (()=>patterns),
    "sanitizeHTML": (()=>sanitizeHTML),
    "validateAndSanitizeInput": (()=>validateAndSanitizeInput),
    "validateEmail": (()=>validateEmail),
    "validateInput": (()=>validateInput),
    "validateURL": (()=>validateURL),
    "validateUUID": (()=>validateUUID)
});
async function validateInput(request, schemas) {
    const errors = [];
    const validatedData = {};
    try {
        // Validate request body
        if (schemas.body) {
            let body = {};
            if (request.method !== 'GET' && request.method !== 'DELETE') {
                try {
                    body = await request.json();
                } catch (error) {
                    errors.push('Invalid JSON in request body');
                    return {
                        success: false,
                        errors
                    };
                }
            }
            const bodyValidation = validateObject(body, schemas.body, 'body');
            if (!bodyValidation.success) {
                errors.push(...bodyValidation.errors);
            } else {
                validatedData.body = bodyValidation.data;
            }
        }
        // Validate query parameters
        if (schemas.query) {
            const searchParams = request.nextUrl.searchParams;
            const query = {};
            for (const [key, value] of searchParams.entries()){
                query[key] = value;
            }
            const queryValidation = validateObject(query, schemas.query, 'query');
            if (!queryValidation.success) {
                errors.push(...queryValidation.errors);
            } else {
                validatedData.query = queryValidation.data;
            }
        }
        return {
            success: errors.length === 0,
            errors: errors.length > 0 ? errors : undefined,
            data: validatedData
        };
    } catch (error) {
        console.error('Validation error:', error);
        return {
            success: false,
            errors: [
                'Validation failed'
            ]
        };
    }
}
function validateObject(obj, schema, prefix = '') {
    const errors = [];
    const validatedData = {};
    for (const [key, rules] of Object.entries(schema)){
        const fieldPath = prefix ? `${prefix}.${key}` : key;
        const value = obj[key];
        // Check required fields
        if (rules.required && (value === undefined || value === null || value === '')) {
            errors.push(`${fieldPath} is required`);
            continue;
        }
        // Skip validation if field is not required and not provided
        if (!rules.required && (value === undefined || value === null)) {
            continue;
        }
        // Type validation
        if (rules.type) {
            const typeValidation = validateType(value, rules.type, fieldPath);
            if (!typeValidation.success) {
                errors.push(...typeValidation.errors);
                continue;
            }
        }
        // String validations
        if (rules.type === 'string' && typeof value === 'string') {
            if (rules.minLength && value.length < rules.minLength) {
                errors.push(`${fieldPath} must be at least ${rules.minLength} characters`);
                continue;
            }
            if (rules.maxLength && value.length > rules.maxLength) {
                errors.push(`${fieldPath} must be no more than ${rules.maxLength} characters`);
                continue;
            }
            if (rules.pattern && !rules.pattern.test(value)) {
                errors.push(`${fieldPath} format is invalid`);
                continue;
            }
            if (rules.enum && !rules.enum.includes(value)) {
                errors.push(`${fieldPath} must be one of: ${rules.enum.join(', ')}`);
                continue;
            }
        }
        // Number validations
        if (rules.type === 'number' && typeof value === 'number') {
            if (rules.min !== undefined && value < rules.min) {
                errors.push(`${fieldPath} must be at least ${rules.min}`);
                continue;
            }
            if (rules.max !== undefined && value > rules.max) {
                errors.push(`${fieldPath} must be no more than ${rules.max}`);
                continue;
            }
        }
        // Array validations
        if (rules.type === 'array' && Array.isArray(value)) {
            if (rules.maxItems && value.length > rules.maxItems) {
                errors.push(`${fieldPath} must have no more than ${rules.maxItems} items`);
                continue;
            }
        }
        // Custom validation
        if (rules.custom) {
            const customResult = rules.custom(value);
            if (customResult !== true) {
                errors.push(typeof customResult === 'string' ? customResult : `${fieldPath} is invalid`);
                continue;
            }
        }
        // Sanitize and add to validated data
        validatedData[key] = sanitizeValue(value, rules.type);
    }
    return {
        success: errors.length === 0,
        errors: errors.length > 0 ? errors : undefined,
        data: validatedData
    };
}
function validateType(value, expectedType, fieldPath) {
    switch(expectedType){
        case 'string':
            if (typeof value !== 'string') {
                return {
                    success: false,
                    errors: [
                        `${fieldPath} must be a string`
                    ]
                };
            }
            break;
        case 'number':
            if (typeof value !== 'number' || isNaN(value)) {
                return {
                    success: false,
                    errors: [
                        `${fieldPath} must be a number`
                    ]
                };
            }
            break;
        case 'boolean':
            if (typeof value !== 'boolean') {
                return {
                    success: false,
                    errors: [
                        `${fieldPath} must be a boolean`
                    ]
                };
            }
            break;
        case 'array':
            if (!Array.isArray(value)) {
                return {
                    success: false,
                    errors: [
                        `${fieldPath} must be an array`
                    ]
                };
            }
            break;
        case 'object':
            if (typeof value !== 'object' || value === null || Array.isArray(value)) {
                return {
                    success: false,
                    errors: [
                        `${fieldPath} must be an object`
                    ]
                };
            }
            break;
    }
    return {
        success: true
    };
}
function sanitizeValue(value, type) {
    if (value === null || value === undefined) {
        return value;
    }
    switch(type){
        case 'string':
            return typeof value === 'string' ? value.trim().slice(0, 10000) // Limit string length
             : String(value).slice(0, 10000);
        case 'number':
            return typeof value === 'number' ? value : Number(value);
        case 'boolean':
            return Boolean(value);
        case 'array':
            return Array.isArray(value) ? value.slice(0, 1000) : [
                value
            ] // Limit array size
            ;
        case 'object':
            return typeof value === 'object' ? value : {};
        default:
            return value;
    }
}
const patterns = {
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    url: /^https?:\/\/.+/,
    uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
    phone: /^\+?[\d\s\-\(\)]+$/,
    date: /^\d{4}-\d{2}-\d{2}$/,
    time: /^\d{2}:\d{2}$/,
    datetime: /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/
};
function validateEmail(email) {
    return patterns.email.test(email);
}
function validateURL(url) {
    return patterns.url.test(url);
}
function validateUUID(uuid) {
    return patterns.uuid.test(uuid);
}
function sanitizeHTML(input) {
    return input.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#x27;').replace(/\//g, '&#x2F;');
}
function validateAndSanitizeInput(input, schema) {
    const validation = validateObject(input, schema);
    return {
        isValid: validation.success,
        sanitized: validation.data || {},
        errors: validation.errors || []
    };
}
}}),
"[project]/src/lib/security/error-handler.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAuthError": (()=>createAuthError),
    "createForbiddenError": (()=>createForbiddenError),
    "createValidationError": (()=>createValidationError),
    "handleSecureError": (()=>handleSecureError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
// Define secure error mappings
const ERROR_MAPPINGS = {
    // Authentication errors
    'PGRST301': {
        code: 'AUTH_REQUIRED',
        message: 'Authentication required',
        statusCode: 401
    },
    'PGRST302': {
        code: 'FORBIDDEN',
        message: 'Access denied',
        statusCode: 403
    },
    // Database constraint errors
    '23503': {
        code: 'INVALID_REFERENCE',
        message: 'Invalid data reference',
        statusCode: 400
    },
    '23505': {
        code: 'DUPLICATE_ENTRY',
        message: 'Duplicate entry not allowed',
        statusCode: 409
    },
    '23514': {
        code: 'VALIDATION_ERROR',
        message: 'Data validation failed',
        statusCode: 400
    },
    // Rate limiting
    'RATE_LIMIT': {
        code: 'RATE_LIMIT',
        message: 'Too many requests',
        statusCode: 429
    },
    // Validation errors
    'VALIDATION_ERROR': {
        code: 'VALIDATION_ERROR',
        message: 'Invalid input data',
        statusCode: 400
    },
    // Generic errors
    'INTERNAL_ERROR': {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred',
        statusCode: 500
    }
};
function handleSecureError(error) {
    // Log the full error for debugging (server-side only)
    console.error('Secure Error Handler:', {
        message: error.message,
        code: error.code,
        details: error.details,
        timestamp: new Date().toISOString(),
        stack: error.stack
    });
    // Determine the error type
    let secureError;
    if (error.code && ERROR_MAPPINGS[error.code]) {
        secureError = ERROR_MAPPINGS[error.code];
    } else if (error.message?.includes('rate limit')) {
        secureError = ERROR_MAPPINGS['RATE_LIMIT'];
    } else if (error.message?.includes('validation')) {
        secureError = ERROR_MAPPINGS['VALIDATION_ERROR'];
    } else {
        secureError = ERROR_MAPPINGS['INTERNAL_ERROR'];
    }
    // Return sanitized error response
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        error: {
            code: secureError.code,
            message: secureError.message
        }
    }, {
        status: secureError.statusCode
    });
}
function createValidationError(message) {
    const error = new Error(message);
    error.name = 'VALIDATION_ERROR';
    return error;
}
function createAuthError(message = 'Authentication required') {
    const error = new Error(message);
    error.name = 'AUTH_REQUIRED';
    return error;
}
function createForbiddenError(message = 'Access denied') {
    const error = new Error(message);
    error.name = 'FORBIDDEN';
    return error;
}
}}),
"[project]/src/lib/security/middleware.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "commonSchemas": (()=>commonSchemas),
    "withSecurity": (()=>withSecurity)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/server.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$rate$2d$limit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/rate-limit.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$validation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/validation.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/error-handler.ts [app-route] (ecmascript)");
;
;
;
;
;
function withSecurity(handler, options = {}) {
    return async (request, context)=>{
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
            // Authentication check
            if (options.requireAuth !== false) {
                const { data: { user }, error } = await supabase.auth.getUser();
                if (error || !user) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: {
                            code: 'AUTH_REQUIRED',
                            message: 'Authentication required'
                        }
                    }, {
                        status: 401
                    });
                }
                // Add user to context
                if (context) {
                    context.user = user;
                }
            }
            // Rate limiting
            if (options.rateLimit) {
                const clientIP = request.ip || request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
                const rateLimitResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$rate$2d$limit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rateLimit"])(clientIP, options.rateLimit.requests, options.rateLimit.window);
                if (!rateLimitResult.success) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: {
                            code: 'RATE_LIMIT',
                            message: 'Too many requests',
                            retryAfter: rateLimitResult.retryAfter
                        }
                    }, {
                        status: 429,
                        headers: {
                            'Retry-After': rateLimitResult.retryAfter?.toString() || '60'
                        }
                    });
                }
            }
            // Input validation
            if (options.validation) {
                const validationResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$validation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateInput"])(request, options.validation);
                if (!validationResult.success) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: {
                            code: 'VALIDATION_ERROR',
                            message: 'Invalid input data',
                            details: validationResult.errors
                        }
                    }, {
                        status: 400
                    });
                }
                // Add validated data to context to avoid re-reading body
                if (context && validationResult.data) {
                    context.validatedData = validationResult.data;
                }
            }
            // Execute the handler
            const response = await handler(request, context);
            // Audit logging
            if (options.auditLog && context?.user) {
                await logAuditEvent(request, context.user, response);
            }
            return response;
        } catch (error) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleSecureError"])(error);
        }
    };
}
async function logAuditEvent(request, user, response) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        const action = `${request.method} ${request.nextUrl.pathname}`;
        const clientIP = request.ip || request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip');
        await supabase.from('audit_logs').insert([
            {
                user_id: user.id,
                action,
                ip_address: clientIP,
                user_agent: request.headers.get('user-agent'),
                new_values: {
                    status: response.status,
                    method: request.method,
                    path: request.nextUrl.pathname,
                    timestamp: new Date().toISOString()
                }
            }
        ]);
    } catch (error) {
        console.error('Failed to log audit event:', error);
    }
}
const commonSchemas = {
    task: {
        title: {
            required: true,
            type: 'string',
            maxLength: 200
        },
        description: {
            required: false,
            type: 'string',
            maxLength: 1000
        },
        priority: {
            required: false,
            type: 'string',
            enum: [
                'low',
                'medium',
                'high'
            ]
        },
        due_date: {
            required: false,
            type: 'string'
        },
        category: {
            required: false,
            type: 'string',
            maxLength: 100
        },
        estimated_duration: {
            required: false,
            type: 'number',
            min: 1,
            max: 1440
        }
    },
    recipe: {
        title: {
            required: true,
            type: 'string',
            maxLength: 200
        },
        description: {
            required: false,
            type: 'string',
            maxLength: 2000
        },
        ingredients: {
            required: true,
            type: 'array',
            maxItems: 50
        },
        instructions: {
            required: true,
            type: 'array',
            maxItems: 50
        },
        prep_time: {
            required: false,
            type: 'number',
            min: 0,
            max: 1440
        },
        cook_time: {
            required: false,
            type: 'number',
            min: 0,
            max: 1440
        },
        servings: {
            required: false,
            type: 'number',
            min: 1,
            max: 50
        },
        difficulty: {
            required: false,
            type: 'string',
            enum: [
                'easy',
                'medium',
                'hard'
            ]
        }
    },
    shoppingListItem: {
        name: {
            required: true,
            type: 'string',
            maxLength: 200
        },
        quantity: {
            required: false,
            type: 'number',
            min: 0
        },
        unit: {
            required: false,
            type: 'string',
            maxLength: 50
        },
        category: {
            required: false,
            type: 'string',
            maxLength: 100
        },
        priority: {
            required: false,
            type: 'number',
            min: 1,
            max: 5
        }
    },
    transaction: {
        amount: {
            required: true,
            type: 'number'
        },
        description: {
            required: true,
            type: 'string',
            maxLength: 200
        },
        category_id: {
            required: true,
            type: 'string'
        },
        transaction_type: {
            required: true,
            type: 'string',
            enum: [
                'income',
                'expense'
            ]
        },
        date: {
            required: true,
            type: 'string'
        }
    }
};
}}),
"[project]/src/lib/security/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addSecurityHeaders": (()=>addSecurityHeaders),
    "sanitizeArray": (()=>sanitizeArray),
    "sanitizeNumber": (()=>sanitizeNumber),
    "sanitizeObject": (()=>sanitizeObject),
    "sanitizeString": (()=>sanitizeString)
});
function addSecurityHeaders(response) {
    // Security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
    response.headers.set('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;");
    // Remove server information
    response.headers.delete('Server');
    response.headers.delete('X-Powered-By');
    return response;
}
function sanitizeString(input) {
    if (!input || typeof input !== 'string') return '';
    // Remove potentially dangerous characters
    return input.replace(/[<>]/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim().slice(0, 10000) // Limit length
    ;
}
function sanitizeNumber(input) {
    const num = Number(input);
    if (isNaN(num) || !isFinite(num)) return null;
    return num;
}
function sanitizeArray(input) {
    if (!Array.isArray(input)) return [];
    return input.slice(0, 100) // Limit array size
    ;
}
function sanitizeObject(input, allowedKeys) {
    if (typeof input !== 'object' || input === null) return {};
    const sanitized = {};
    for (const key of allowedKeys){
        if (key in input) {
            sanitized[key] = input[key];
        }
    }
    return sanitized;
}
}}),
"[project]/src/app/api/chat/conversations/[id]/messages/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/server.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/middleware.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/utils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$rate$2d$limit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/rate-limit.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/error-handler.ts [app-route] (ecmascript)");
;
;
;
;
;
;
const GET = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withSecurity"])(async (request, context)=>{
    try {
        const { params } = context;
        const conversationId = params.id;
        if (!conversationId || typeof conversationId !== 'string') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: {
                    code: 'VALIDATION_ERROR',
                    message: 'Invalid conversation ID'
                }
            }, {
                status: 400
            });
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        const user = context.user;
        // First verify the conversation belongs to the user
        const { data: conversation, error: fetchError } = await supabase.from('chat_conversations').select('id').eq('id', conversationId).eq('user_id', user.id).single();
        if (fetchError || !conversation) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: {
                    code: 'NOT_FOUND',
                    message: 'Conversation not found'
                }
            }, {
                status: 404
            });
        }
        // Get messages for the conversation
        const { data: messages, error } = await supabase.from('chat_messages').select('id, role, content, created_at').eq('conversation_id', conversationId).order('created_at', {
            ascending: true
        });
        if (error) throw error;
        const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            messages: messages || []
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addSecurityHeaders"])(response);
    } catch (error) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleSecureError"])(error);
    }
}, {
    requireAuth: true,
    rateLimit: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$rate$2d$limit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rateLimitConfigs"].api,
    auditLog: true
});
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__85eb8102._.js.map