{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/dashboard/page.tsx"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\nimport { redirect } from 'next/navigation'\nimport {\n  CheckSquareIcon,\n  DollarSignIcon,\n  ShoppingCartIcon,\n  ChefHatIcon,\n} from 'lucide-react'\n\nexport default async function DashboardPage() {\n  const supabase = await createClient()\n\n  const {\n    data: { user },\n  } = await supabase.auth.getUser()\n\n  if (!user) {\n    redirect('/login')\n  }\n\n  // Get user stats\n  const { data: stats } = await supabase.rpc('get_user_stats', {\n    user_uuid: user.id,\n  })\n\n  const quickStats = [\n    {\n      name: 'Total Tasks',\n      value: stats?.total_tasks || 0,\n      icon: CheckSquareIcon,\n      color: 'bg-blue-500',\n    },\n    {\n      name: 'Monthly Income',\n      value: `$${stats?.monthly_income || 0}`,\n      icon: DollarSignIcon,\n      color: 'bg-green-500',\n    },\n    {\n      name: 'Shopping Lists',\n      value: stats?.total_shopping_lists || 0,\n      icon: ShoppingCartIcon,\n      color: 'bg-purple-500',\n    },\n    {\n      name: 'Recipes',\n      value: stats?.total_recipes || 0,\n      icon: ChefHatIcon,\n      color: 'bg-orange-500',\n    },\n  ]\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n        <p className=\"mt-2 text-gray-600\">\n          Welcome back! Here's what's happening with your life management.\n        </p>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n        {quickStats.map((stat) => (\n          <div key={stat.name} className=\"bg-white overflow-hidden shadow-sm rounded-xl border border-gray-200 hover:shadow-md transition-shadow\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className={`p-3 rounded-xl ${stat.color}`}>\n                    <stat.icon className=\"h-6 w-6 text-white\" />\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                      {stat.name}\n                    </dt>\n                    <dd className=\"text-2xl font-bold text-gray-900 mt-1\">\n                      {stat.value}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Recent Tasks */}\n        <div className=\"bg-white shadow-sm rounded-xl border border-gray-200\">\n          <div className=\"p-6\">\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n              Recent Tasks\n            </h3>\n            <div className=\"text-gray-500\">\n              <p>No recent tasks. <a href=\"/tasks\" className=\"text-blue-600 hover:text-blue-700 font-medium\">Create your first task</a></p>\n            </div>\n          </div>\n        </div>\n\n        {/* Budget Overview */}\n        <div className=\"bg-white shadow-sm rounded-xl border border-gray-200\">\n          <div className=\"p-6\">\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-6\">\n              Budget Overview\n            </h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Monthly Income</span>\n                <span className=\"text-lg font-semibold text-green-600\">\n                  ${stats?.monthly_income || 0}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Monthly Expenses</span>\n                <span className=\"text-lg font-semibold text-red-600\">\n                  ${stats?.monthly_expenses || 0}\n                </span>\n              </div>\n              <div className=\"border-t border-gray-200 pt-4\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-lg font-medium text-gray-900\">Net</span>\n                  <span className={`text-lg font-bold ${\n                    (stats?.monthly_income || 0) - (stats?.monthly_expenses || 0) >= 0\n                      ? 'text-green-600'\n                      : 'text-red-600'\n                  }`}>\n                    ${(stats?.monthly_income || 0) - (stats?.monthly_expenses || 0)}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;;;;;AAOe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,iBAAiB;IACjB,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,kBAAkB;QAC3D,WAAW,KAAK,EAAE;IACpB;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,OAAO,OAAO,eAAe;YAC7B,MAAM,+NAAA,CAAA,kBAAe;YACrB,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,CAAC,CAAC,EAAE,OAAO,kBAAkB,GAAG;YACvC,MAAM,sNAAA,CAAA,iBAAc;YACpB,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,OAAO,wBAAwB;YACtC,MAAM,0NAAA,CAAA,mBAAgB;YACtB,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,OAAO,iBAAiB;YAC/B,MAAM,gNAAA,CAAA,cAAW;YACjB,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAMpC,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wBAAoB,WAAU;kCAC7B,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAW,CAAC,eAAe,EAAE,KAAK,KAAK,EAAE;sDAC5C,cAAA,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGzB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,KAAK,IAAI;;;;;;8DAEZ,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAdb,KAAK,IAAI;;;;;;;;;;0BAyBvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;;4CAAE;0DAAiB,8OAAC;gDAAE,MAAK;gDAAS,WAAU;0DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMrG,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;;wDAAuC;wDACnD,OAAO,kBAAkB;;;;;;;;;;;;;sDAG/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;;wDAAqC;wDACjD,OAAO,oBAAoB;;;;;;;;;;;;;sDAGjC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,8OAAC;wDAAK,WAAW,CAAC,kBAAkB,EAClC,CAAC,OAAO,kBAAkB,CAAC,IAAI,CAAC,OAAO,oBAAoB,CAAC,KAAK,IAC7D,mBACA,gBACJ;;4DAAE;4DACA,CAAC,OAAO,kBAAkB,CAAC,IAAI,CAAC,OAAO,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlF", "debugId": null}}]}