import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'
import { ensureUserProfile } from '@/lib/auth/profile-manager'

// GET /api/budget/transactions - Get user's transactions
export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: transactions, error } = await supabase
        .from('transactions')
        .select(`
          id,
          amount,
          description,
          transaction_type,
          date,
          created_at,
          updated_at,
          budget_categories (
            id,
            name,
            type,
            color
          )
        `)
        .eq('user_id', user.id)
        .order('date', { ascending: false })
        .limit(100)

      if (error) throw error

      const response = NextResponse.json({ transactions: transactions || [] })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// POST /api/budget/transactions - Create new transaction
export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const requestData = await request.json()
      const { amount, description, transaction_type, category_id, date } = requestData
      
      // Basic validation
      if (!amount || typeof amount !== 'number' || amount <= 0) {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid amount' } 
        }, { status: 400 })
      }

      if (!description || typeof description !== 'string' || description.length > 200) {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid description' } 
        }, { status: 400 })
      }

      if (!transaction_type || !['income', 'expense'].includes(transaction_type)) {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Transaction type must be income or expense' } 
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: transaction, error } = await supabase
        .from('transactions')
        .insert([
          {
            user_id: user.id,
            amount,
            description: description.trim(),
            transaction_type,
            category_id: category_id || null,
            date: date || new Date().toISOString().split('T')[0]
          },
        ])
        .select(`
          id,
          amount,
          description,
          transaction_type,
          date,
          created_at,
          updated_at,
          budget_categories (
            id,
            name,
            type,
            color
          )
        `)
        .single()

      if (error) throw error

      const response = NextResponse.json({ transaction })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
