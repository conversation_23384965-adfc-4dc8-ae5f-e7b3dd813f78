import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { addSecurityHeaders } from '@/lib/security/utils'
import { handleSecureError } from '@/lib/security/error-handler'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { rateLimit } from '@/lib/security/rate-limit'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for auth endpoints
    const clientIP = request.ip || 
      request.headers.get('x-forwarded-for') || 
      request.headers.get('x-real-ip') || 
      'unknown'
    
    const rateLimitResult = await rateLimit(
      `signup:${clientIP}`,
      rateLimitConfigs.auth.requests,
      rateLimitConfigs.auth.window
    )
    
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          error: {
            code: 'RATE_LIMIT',
            message: 'Too many signup attempts. Please try again later.',
            retryAfter: rateLimitResult.retryAfter
          }
        },
        {
          status: 429,
          headers: {
            'Retry-After': rateLimitResult.retryAfter?.toString() || '900'
          }
        }
      )
    }

    const { email, password, fullName } = await request.json()

    // Input validation
    if (!email || !password || !fullName) {
      return NextResponse.json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Email, password, and full name are required'
        }
      }, { status: 400 })
    }

    if (typeof email !== 'string' || typeof password !== 'string' || typeof fullName !== 'string') {
      return NextResponse.json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid input format'
        }
      }, { status: 400 })
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid email format'
        }
      }, { status: 400 })
    }

    // Password strength validation
    if (password.length < 8) {
      return NextResponse.json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Password must be at least 8 characters long'
        }
      }, { status: 400 })
    }

    // Full name validation
    if (fullName.trim().length < 2 || fullName.length > 100) {
      return NextResponse.json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Full name must be between 2 and 100 characters'
        }
      }, { status: 400 })
    }

    const supabase = await createClient()

    const { data, error } = await supabase.auth.signUp({
      email: email.toLowerCase().trim(),
      password,
      options: {
        data: {
          full_name: fullName.trim(),
        },
      },
    })

    if (error) {
      // Log failed signup attempt
      console.warn('Failed signup attempt:', {
        email: email.toLowerCase().trim(),
        ip: clientIP,
        timestamp: new Date().toISOString(),
        error: error.message
      })

      return NextResponse.json({
        error: {
          code: 'SIGNUP_FAILED',
          message: error.message
        }
      }, { status: 400 })
    }

    // Log successful signup
    console.log('Successful signup:', {
      userId: data.user?.id,
      email: data.user?.email,
      ip: clientIP,
      timestamp: new Date().toISOString()
    })

    const response = NextResponse.json({
      message: 'Signup successful! Please check your email to confirm your account.',
      user: data.user ? {
        id: data.user.id,
        email: data.user.email,
        user_metadata: data.user.user_metadata
      } : null
    })

    return addSecurityHeaders(response)
  } catch (error) {
    return handleSecureError(error)
  }
}
