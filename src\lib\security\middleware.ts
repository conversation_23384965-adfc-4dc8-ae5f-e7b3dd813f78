import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { rateLimit } from './rate-limit'
import { validateInput } from './validation'
import { handleSecureError } from './error-handler'

export interface SecurityOptions {
  requireAuth?: boolean
  rateLimit?: {
    requests: number
    window: string
  }
  validation?: {
    body?: any
    query?: any
  }
  auditLog?: boolean
}

export function withSecurity(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>,
  options: SecurityOptions = {}
) {
  return async (request: NextRequest, context?: any) => {
    try {
      const supabase = await createClient()
      
      // Authentication check
      if (options.requireAuth !== false) {
        const { data: { user }, error } = await supabase.auth.getUser()
        if (error || !user) {
          return NextResponse.json({
            error: {
              code: 'AUTH_REQUIRED',
              message: 'Authentication required'
            }
          }, { status: 401 })
        }

        // Add user to context
        if (context) {
          context.user = user
        }
      }

      // Rate limiting
      if (options.rateLimit) {
        const clientIP = request.ip || 
          request.headers.get('x-forwarded-for') || 
          request.headers.get('x-real-ip') || 
          'unknown'
        
        const rateLimitResult = await rateLimit(
          clientIP,
          options.rateLimit.requests,
          options.rateLimit.window
        )
        
        if (!rateLimitResult.success) {
          return NextResponse.json(
            {
              error: {
                code: 'RATE_LIMIT',
                message: 'Too many requests',
                retryAfter: rateLimitResult.retryAfter
              }
            },
            {
              status: 429,
              headers: {
                'Retry-After': rateLimitResult.retryAfter?.toString() || '60'
              }
            }
          )
        }
      }

      // Input validation
      if (options.validation) {
        const validationResult = await validateInput(request, options.validation)
        if (!validationResult.success) {
          return NextResponse.json(
            {
              error: {
                code: 'VALIDATION_ERROR',
                message: 'Invalid input data',
                details: validationResult.errors
              }
            },
            { status: 400 }
          )
        }

        // Add validated data to context to avoid re-reading body
        if (context && validationResult.data) {
          context.validatedData = validationResult.data
        }
      }

      // Execute the handler
      const response = await handler(request, context)

      // Audit logging
      if (options.auditLog && context?.user) {
        await logAuditEvent(request, context.user, response)
      }

      return response
    } catch (error) {
      return handleSecureError(error)
    }
  }
}

async function logAuditEvent(
  request: NextRequest,
  user: any,
  response: NextResponse
) {
  try {
    const supabase = await createClient()
    
    const action = `${request.method} ${request.nextUrl.pathname}`
    const clientIP = request.ip || 
      request.headers.get('x-forwarded-for') || 
      request.headers.get('x-real-ip')
    
    await supabase
      .from('audit_logs')
      .insert([
        {
          user_id: user.id,
          action,
          ip_address: clientIP,
          user_agent: request.headers.get('user-agent'),
          new_values: {
            status: response.status,
            method: request.method,
            path: request.nextUrl.pathname,
            timestamp: new Date().toISOString()
          }
        }
      ])
  } catch (error) {
    console.error('Failed to log audit event:', error)
  }
}





// Common validation schemas
export const commonSchemas = {
  task: {
    title: { required: true, type: 'string', maxLength: 200 },
    description: { required: false, type: 'string', maxLength: 1000 },
    priority: { required: false, type: 'string', enum: ['low', 'medium', 'high'] },
    due_date: { required: false, type: 'string' },
    category: { required: false, type: 'string', maxLength: 100 },
    estimated_duration: { required: false, type: 'number', min: 1, max: 1440 }
  },
  recipe: {
    title: { required: true, type: 'string', maxLength: 200 },
    description: { required: false, type: 'string', maxLength: 2000 },
    ingredients: { required: true, type: 'array', maxItems: 50 },
    instructions: { required: true, type: 'array', maxItems: 50 },
    prep_time: { required: false, type: 'number', min: 0, max: 1440 },
    cook_time: { required: false, type: 'number', min: 0, max: 1440 },
    servings: { required: false, type: 'number', min: 1, max: 50 },
    difficulty: { required: false, type: 'string', enum: ['easy', 'medium', 'hard'] }
  },
  shoppingListItem: {
    name: { required: true, type: 'string', maxLength: 200 },
    quantity: { required: false, type: 'number', min: 0 },
    unit: { required: false, type: 'string', maxLength: 50 },
    category: { required: false, type: 'string', maxLength: 100 },
    priority: { required: false, type: 'number', min: 1, max: 5 }
  },
  transaction: {
    amount: { required: true, type: 'number' },
    description: { required: true, type: 'string', maxLength: 200 },
    category_id: { required: true, type: 'string' },
    transaction_type: { required: true, type: 'string', enum: ['income', 'expense'] },
    date: { required: true, type: 'string' }
  }
}


