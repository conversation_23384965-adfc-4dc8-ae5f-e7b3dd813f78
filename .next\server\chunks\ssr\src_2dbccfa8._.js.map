{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/services/recipes-service.ts"], "sourcesContent": ["export interface Recipe {\n  id: string\n  title: string\n  description?: string\n  ingredients: string[]\n  instructions: string[]\n  prep_time?: number\n  cook_time?: number\n  servings?: number\n  difficulty?: 'easy' | 'medium' | 'hard'\n  tags?: string[]\n  image_url?: string\n  source_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CreateRecipeData {\n  title: string\n  description?: string\n  ingredients: string[]\n  instructions: string[]\n  prep_time?: number\n  cook_time?: number\n  servings?: number\n  difficulty?: 'easy' | 'medium' | 'hard'\n  tags?: string[]\n  image_url?: string\n  source_url?: string\n}\n\nexport interface UpdateRecipeData extends Partial<CreateRecipeData> {}\n\nclass RecipesService {\n  private baseUrl = '/api/recipes'\n\n  async getRecipes(): Promise<Recipe[]> {\n    const response = await fetch(this.baseUrl, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch recipes: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  async getRecipe(id: string): Promise<Recipe> {\n    const response = await fetch(`${this.baseUrl}/${id}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch recipe: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  async createRecipe(data: CreateRecipeData): Promise<Recipe> {\n    const response = await fetch(this.baseUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to create recipe: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  async updateRecipe(id: string, data: UpdateRecipeData): Promise<Recipe> {\n    const response = await fetch(`${this.baseUrl}/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to update recipe: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  async deleteRecipe(id: string): Promise<void> {\n    const response = await fetch(`${this.baseUrl}/${id}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to delete recipe: ${response.statusText}`)\n    }\n  }\n\n  async importRecipeFromUrl(url: string): Promise<Recipe> {\n    const response = await fetch(`${this.baseUrl}/import`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ url }),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to import recipe: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  async searchRecipes(query: string): Promise<Recipe[]> {\n    const response = await fetch(`${this.baseUrl}/search?q=${encodeURIComponent(query)}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to search recipes: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n}\n\nexport const recipesService = new RecipesService()\n"], "names": [], "mappings": ";;;AAiCA,MAAM;IACI,UAAU,eAAc;IAEhC,MAAM,aAAgC;QACpC,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;YACzC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,UAAU,EAAU,EAAmB;QAC3C,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;QAClE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,IAAsB,EAAmB;QAC1D,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;YACzC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,EAAU,EAAE,IAAsB,EAAmB;QACtE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,EAAU,EAAiB;QAC5C,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;QACnE;IACF;IAEA,MAAM,oBAAoB,GAAW,EAAmB;QACtD,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAI;QAC7B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,cAAc,KAAa,EAAqB;QACpD,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,mBAAmB,QAAQ,EAAE;YACpF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;QACpE;QAEA,OAAO,SAAS,IAAI;IACtB;AACF;AAEO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { cva, type VariantProps } from 'class-variance-authority'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed ring-offset-background transform hover:scale-105 active:scale-95 focus:scale-105',\n  {\n    variants: {\n      variant: {\n        default: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl focus-visible:ring-blue-500',\n        destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl focus-visible:ring-red-500',\n        outline: 'border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus-visible:ring-gray-500',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 focus-visible:ring-gray-500',\n        ghost: 'hover:bg-gray-100 text-gray-700 focus-visible:ring-gray-500',\n        link: 'underline-offset-4 hover:underline text-blue-600 focus-visible:ring-blue-500',\n        success: 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl focus-visible:ring-green-500',\n        warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 shadow-lg hover:shadow-xl focus-visible:ring-yellow-500',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 text-xs',\n        lg: 'h-12 px-8 text-base',\n        xl: 'h-14 px-10 text-lg',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n  loadingText?: string\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, loadingText, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        aria-disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            {loadingText || 'Loading...'}\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,gVACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACtB,iBAAe,YAAY;QAC1B,GAAG,KAAK;kBAER,wBACC;;8BACE,8OAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,eAAY;;sCAEZ,8OAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAGL,eAAe;;2BAGlB;;;;;;AAIR;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Modal.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode, useEffect } from 'react'\nimport { X } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title?: string\n  description?: string\n  children: ReactNode\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'\n  className?: string\n}\n\nconst sizeClasses = {\n  sm: 'max-w-md',\n  md: 'max-w-lg',\n  lg: 'max-w-2xl',\n  xl: 'max-w-4xl',\n  full: 'max-w-7xl',\n}\n\nexport function Modal({\n  isOpen,\n  onClose,\n  title,\n  description,\n  children,\n  size = 'md',\n  className,\n}: ModalProps) {\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose()\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape)\n      document.body.style.overflow = 'hidden'\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen, onClose])\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      <div className=\"flex min-h-full items-center justify-center p-4\">\n        {/* Backdrop */}\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm transition-opacity duration-300\"\n          onClick={onClose}\n        />\n\n        {/* Modal */}\n        <div\n          className={cn(\n            'relative w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left shadow-xl transition-all duration-300 animate-slide-up',\n            sizeClasses[size],\n            className\n          )}\n          onClick={(e) => e.stopPropagation()}\n        >\n          {(title || description) && (\n            <div className=\"mb-6\">\n              <div className=\"flex items-center justify-between\">\n                {title && (\n                  <h3 className=\"text-lg font-semibold leading-6 text-gray-900\">\n                    {title}\n                  </h3>\n                )}\n                <button\n                  type=\"button\"\n                  className=\"rounded-md p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\"\n                  onClick={onClose}\n                >\n                  <X className=\"h-5 w-5\" />\n                </button>\n              </div>\n              {description && (\n                <div className=\"mt-2\">\n                  <p className=\"text-sm text-gray-600\">{description}</p>\n                </div>\n              )}\n            </div>\n          )}\n\n          <div>{children}</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface ConfirmModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: () => void\n  title: string\n  message: string\n  confirmText?: string\n  cancelText?: string\n  variant?: 'danger' | 'warning' | 'info'\n}\n\nexport function ConfirmModal({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  message,\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  variant = 'info',\n}: ConfirmModalProps) {\n  const handleConfirm = () => {\n    onConfirm()\n    onClose()\n  }\n\n  const buttonVariant = variant === 'danger' ? 'destructive' : 'default'\n\n  return (\n    <Modal isOpen={isOpen} onClose={onClose} size=\"sm\">\n      <div className=\"text-center\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{title}</h3>\n        <p className=\"text-gray-600 mb-6\">{message}</p>\n        <div className=\"flex justify-center space-x-3\">\n          <button\n            type=\"button\"\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\"\n            onClick={onClose}\n          >\n            {cancelText}\n          </button>\n          <button\n            type=\"button\"\n            className={cn(\n              'px-4 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors',\n              variant === 'danger'\n                ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'\n                : variant === 'warning'\n                ? 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'\n                : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'\n            )}\n            onClick={handleConfirm}\n          >\n            {confirmText}\n          </button>\n        </div>\n      </div>\n    </Modal>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAgBA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;AACR;AAEO,SAAS,MAAM,EACpB,MAAM,EACN,OAAO,EACP,KAAK,EACL,WAAW,EACX,QAAQ,EACR,OAAO,IAAI,EACX,SAAS,EACE;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,WAAW;YACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uIACA,WAAW,CAAC,KAAK,EACjB;oBAEF,SAAS,CAAC,IAAM,EAAE,eAAe;;wBAEhC,CAAC,SAAS,WAAW,mBACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,uBACC,8OAAC;4CAAG,WAAU;sDACX;;;;;;sDAGL,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS;sDAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAGhB,6BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;sCAM9C,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAKhB;AAaO,SAAS,aAAa,EAC3B,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,UAAU,MAAM,EACE;IAClB,MAAM,gBAAgB;QACpB;QACA;IACF;IAEA,MAAM,gBAAgB,YAAY,WAAW,gBAAgB;IAE7D,qBACE,8OAAC;QAAM,QAAQ;QAAQ,SAAS;QAAS,MAAK;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAC1D,8OAAC;oBAAE,WAAU;8BAAsB;;;;;;8BACnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS;sCAER;;;;;;sCAEH,8OAAC;4BACC,MAAK;4BACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6HACA,YAAY,WACR,mDACA,YAAY,YACZ,4DACA;4BAEN,SAAS;sCAER;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border border-gray-200 bg-white text-gray-950 shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-lg font-semibold leading-none tracking-tight text-gray-900', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p ref={ref} className={cn('text-sm text-gray-600', className)} {...props} />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex items-center p-6 pt-0', className)} {...props} />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAGvF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAE,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QAAa,GAAG,KAAK;;;;;;AAG7E,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAGpF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',\n  {\n    variants: {\n      variant: {\n        default: 'bg-blue-100 text-blue-800 hover:bg-blue-200',\n        secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200',\n        destructive: 'bg-red-100 text-red-800 hover:bg-red-200',\n        success: 'bg-green-100 text-green-800 hover:bg-green-200',\n        warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',\n        outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',\n        purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',\n        indigo: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',\n        pink: 'bg-pink-100 text-pink-800 hover:bg-pink-200',\n        orange: 'bg-orange-100 text-orange-800 hover:bg-orange-200',\n      },\n      size: {\n        default: 'px-2.5 py-0.5 text-xs',\n        sm: 'px-2 py-0.5 text-xs',\n        lg: 'px-3 py-1 text-sm',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = 'Badge'\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,uGACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IACvC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/recipes/EnhancedRecipeCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { Badge } from '@/components/ui/Badge'\nimport { \n  ClockIcon, \n  UsersIcon, \n  StarIcon,\n  CalendarIcon,\n  BarChart3Icon,\n  ShoppingCartIcon,\n  ExternalLinkIcon\n} from 'lucide-react'\nimport Link from 'next/link'\nimport toast from 'react-hot-toast'\n\ninterface Recipe {\n  id: string\n  title: string\n  description: string | null\n  prep_time: number | null\n  cook_time: number | null\n  servings: number | null\n  difficulty: 'easy' | 'medium' | 'hard'\n  cuisine: string | null\n  tags: string[]\n  image_url: string | null\n  source_url: string | null\n  nutritional_info: any | null\n  created_at: string\n}\n\ninterface EnhancedRecipeCardProps {\n  recipe: Recipe\n  onAddToMealPlan?: (recipeId: string) => void\n  onAddToShoppingList?: (recipeId: string) => void\n  onAnalyzeNutrition?: (recipeId: string) => void\n  averageRating?: number\n  userRating?: number\n}\n\nconst difficultyColors = {\n  easy: 'bg-green-100 text-green-800',\n  medium: 'bg-yellow-100 text-yellow-800',\n  hard: 'bg-red-100 text-red-800'\n}\n\nexport default function EnhancedRecipeCard({\n  recipe,\n  onAddToMealPlan,\n  onAddToShoppingList,\n  onAnalyzeNutrition,\n  averageRating,\n  userRating\n}: EnhancedRecipeCardProps) {\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n\n  const totalTime = (recipe.prep_time || 0) + (recipe.cook_time || 0)\n\n  const handleAnalyzeNutrition = async () => {\n    if (!onAnalyzeNutrition) return\n    \n    setIsAnalyzing(true)\n    try {\n      await onAnalyzeNutrition(recipe.id)\n      toast.success('Nutritional analysis completed!')\n    } catch (error) {\n      toast.error('Failed to analyze nutrition')\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  const handleAddToMealPlan = () => {\n    if (onAddToMealPlan) {\n      onAddToMealPlan(recipe.id)\n      toast.success('Added to meal plan!')\n    }\n  }\n\n  const handleAddToShoppingList = () => {\n    if (onAddToShoppingList) {\n      onAddToShoppingList(recipe.id)\n      toast.success('Ingredients added to shopping list!')\n    }\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      whileHover={{ y: -5 }}\n      transition={{ duration: 0.2 }}\n    >\n      <Card className=\"h-full overflow-hidden hover:shadow-lg transition-shadow duration-200\">\n        {recipe.image_url && (\n          <div className=\"relative h-48 bg-gray-200\">\n            <img\n              src={recipe.image_url}\n              alt={recipe.title}\n              className=\"w-full h-full object-cover\"\n            />\n            {recipe.source_url && (\n              <a\n                href={recipe.source_url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"absolute top-2 right-2 p-1 bg-white rounded-full shadow-md hover:bg-gray-50\"\n              >\n                <ExternalLinkIcon className=\"h-4 w-4 text-gray-600\" />\n              </a>\n            )}\n          </div>\n        )}\n        \n        <CardHeader className=\"pb-3\">\n          <div className=\"flex items-start justify-between\">\n            <CardTitle className=\"text-lg font-semibold line-clamp-2\">\n              <Link href={`/recipes/${recipe.id}`} className=\"hover:text-blue-600\">\n                {recipe.title}\n              </Link>\n            </CardTitle>\n            {averageRating && (\n              <div className=\"flex items-center space-x-1 flex-shrink-0 ml-2\">\n                <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                <span className=\"text-sm text-gray-600\">{averageRating.toFixed(1)}</span>\n              </div>\n            )}\n          </div>\n          \n          {recipe.description && (\n            <p className=\"text-sm text-gray-600 line-clamp-2 mt-1\">\n              {recipe.description}\n            </p>\n          )}\n        </CardHeader>\n\n        <CardContent className=\"space-y-4\">\n          {/* Recipe Info */}\n          <div className=\"flex items-center justify-between text-sm text-gray-500\">\n            <div className=\"flex items-center space-x-4\">\n              {totalTime > 0 && (\n                <div className=\"flex items-center space-x-1\">\n                  <ClockIcon className=\"h-4 w-4\" />\n                  <span>{totalTime}m</span>\n                </div>\n              )}\n              {recipe.servings && (\n                <div className=\"flex items-center space-x-1\">\n                  <UsersIcon className=\"h-4 w-4\" />\n                  <span>{recipe.servings}</span>\n                </div>\n              )}\n            </div>\n            <Badge className={difficultyColors[recipe.difficulty]}>\n              {recipe.difficulty}\n            </Badge>\n          </div>\n\n          {/* Tags */}\n          {recipe.tags && recipe.tags.length > 0 && (\n            <div className=\"flex flex-wrap gap-1\">\n              {recipe.tags.slice(0, 3).map((tag, index) => (\n                <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                  {tag}\n                </Badge>\n              ))}\n              {recipe.tags.length > 3 && (\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  +{recipe.tags.length - 3}\n                </Badge>\n              )}\n            </div>\n          )}\n\n          {/* Nutritional Info */}\n          {recipe.nutritional_info && (\n            <div className=\"bg-gray-50 rounded-lg p-3\">\n              <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                <div>\n                  <span className=\"font-medium\">Calories:</span> {recipe.nutritional_info.calories}\n                </div>\n                <div>\n                  <span className=\"font-medium\">Protein:</span> {recipe.nutritional_info.protein}\n                </div>\n                <div>\n                  <span className=\"font-medium\">Carbs:</span> {recipe.nutritional_info.carbs}\n                </div>\n                <div>\n                  <span className=\"font-medium\">Fat:</span> {recipe.nutritional_info.fat}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-wrap gap-2\">\n            {onAddToMealPlan && (\n              <Button\n                size=\"sm\"\n                variant=\"outline\"\n                onClick={handleAddToMealPlan}\n                className=\"flex-1\"\n              >\n                <CalendarIcon className=\"h-3 w-3 mr-1\" />\n                Plan\n              </Button>\n            )}\n            \n            {onAddToShoppingList && (\n              <Button\n                size=\"sm\"\n                variant=\"outline\"\n                onClick={handleAddToShoppingList}\n                className=\"flex-1\"\n              >\n                <ShoppingCartIcon className=\"h-3 w-3 mr-1\" />\n                Shop\n              </Button>\n            )}\n            \n            {onAnalyzeNutrition && !recipe.nutritional_info && (\n              <Button\n                size=\"sm\"\n                variant=\"outline\"\n                onClick={handleAnalyzeNutrition}\n                disabled={isAnalyzing}\n                className=\"flex-1\"\n              >\n                <BarChart3Icon className=\"h-3 w-3 mr-1\" />\n                {isAnalyzing ? 'Analyzing...' : 'Nutrition'}\n              </Button>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAjBA;;;;;;;;;;AA4CA,MAAM,mBAAmB;IACvB,MAAM;IACN,QAAQ;IACR,MAAM;AACR;AAEe,SAAS,mBAAmB,EACzC,MAAM,EACN,eAAe,EACf,mBAAmB,EACnB,kBAAkB,EAClB,aAAa,EACb,UAAU,EACc;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,YAAY,CAAC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,SAAS,IAAI,CAAC;IAElE,MAAM,yBAAyB;QAC7B,IAAI,CAAC,oBAAoB;QAEzB,eAAe;QACf,IAAI;YACF,MAAM,mBAAmB,OAAO,EAAE;YAClC,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,iBAAiB;YACnB,gBAAgB,OAAO,EAAE;YACzB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI,qBAAqB;YACvB,oBAAoB,OAAO,EAAE;YAC7B,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;gBACb,OAAO,SAAS,kBACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,KAAK,OAAO,SAAS;4BACrB,KAAK,OAAO,KAAK;4BACjB,WAAU;;;;;;wBAEX,OAAO,UAAU,kBAChB,8OAAC;4BACC,MAAM,OAAO,UAAU;4BACvB,QAAO;4BACP,KAAI;4BACJ,WAAU;sCAEV,cAAA,8OAAC,0NAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAMpC,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE;wCAAE,WAAU;kDAC5C,OAAO,KAAK;;;;;;;;;;;gCAGhB,+BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAK,WAAU;sDAAyB,cAAc,OAAO,CAAC;;;;;;;;;;;;;;;;;;wBAKpE,OAAO,WAAW,kBACjB,8OAAC;4BAAE,WAAU;sCACV,OAAO,WAAW;;;;;;;;;;;;8BAKzB,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,YAAY,mBACX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;;wDAAM;wDAAU;;;;;;;;;;;;;wCAGpB,OAAO,QAAQ,kBACd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;8DAAM,OAAO,QAAQ;;;;;;;;;;;;;;;;;;8CAI5B,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAW,gBAAgB,CAAC,OAAO,UAAU,CAAC;8CAClD,OAAO,UAAU;;;;;;;;;;;;wBAKrB,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,mBACnC,8OAAC;4BAAI,WAAU;;gCACZ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBACjC,8OAAC,iIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAU,WAAU;kDAC5C;uCADS;;;;;gCAIb,OAAO,IAAI,CAAC,MAAM,GAAG,mBACpB,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAAU;wCACzC,OAAO,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;wBAO9B,OAAO,gBAAgB,kBACtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAgB;4CAAE,OAAO,gBAAgB,CAAC,QAAQ;;;;;;;kDAElF,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAe;4CAAE,OAAO,gBAAgB,CAAC,OAAO;;;;;;;kDAEhF,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAa;4CAAE,OAAO,gBAAgB,CAAC,KAAK;;;;;;;kDAE5E,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAW;4CAAE,OAAO,gBAAgB,CAAC,GAAG;;;;;;;;;;;;;;;;;;sCAO9E,8OAAC;4BAAI,WAAU;;gCACZ,iCACC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;;sDAEV,8OAAC,8MAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;gCAK5C,qCACC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0NAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;gCAKhD,sBAAsB,CAAC,OAAO,gBAAgB,kBAC7C,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,8OAAC,sNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCACxB,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/recipes/DietaryFilters.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Button } from '@/components/ui/Button'\nimport { Badge } from '@/components/ui/Badge'\nimport { Input } from '@/components/ui/Input'\nimport { \n  FilterIcon, \n  XIcon, \n  SearchIcon,\n  ClockIcon,\n  UsersIcon,\n  ChefHatIcon\n} from 'lucide-react'\n\ninterface DietaryFiltersProps {\n  onFiltersChange: (filters: RecipeFilters) => void\n  totalRecipes: number\n  filteredCount: number\n}\n\nexport interface RecipeFilters {\n  search: string\n  difficulty: string[]\n  cuisine: string[]\n  dietaryRestrictions: string[]\n  maxPrepTime: number | null\n  maxCookTime: number | null\n  minServings: number | null\n  maxServings: number | null\n  tags: string[]\n}\n\nconst difficultyOptions = [\n  { value: 'easy', label: 'Easy', color: 'bg-green-100 text-green-800' },\n  { value: 'medium', label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },\n  { value: 'hard', label: 'Hard', color: 'bg-red-100 text-red-800' }\n]\n\nconst cuisineOptions = [\n  'Italian', 'Mexican', 'Asian', 'American', 'Mediterranean', \n  'Indian', 'French', 'Thai', 'Chinese', 'Japanese', 'Greek', 'Spanish'\n]\n\nconst dietaryOptions = [\n  { value: 'vegetarian', label: 'Vegetarian', icon: '🥬' },\n  { value: 'vegan', label: 'Vegan', icon: '🌱' },\n  { value: 'gluten-free', label: 'Gluten Free', icon: '🌾' },\n  { value: 'dairy-free', label: 'Dairy Free', icon: '🥛' },\n  { value: 'keto', label: 'Keto', icon: '🥑' },\n  { value: 'paleo', label: 'Paleo', icon: '🥩' },\n  { value: 'low-carb', label: 'Low Carb', icon: '🥒' },\n  { value: 'high-protein', label: 'High Protein', icon: '💪' }\n]\n\nexport default function DietaryFilters({ \n  onFiltersChange, \n  totalRecipes, \n  filteredCount \n}: DietaryFiltersProps) {\n  const [isExpanded, setIsExpanded] = useState(false)\n  const [filters, setFilters] = useState<RecipeFilters>({\n    search: '',\n    difficulty: [],\n    cuisine: [],\n    dietaryRestrictions: [],\n    maxPrepTime: null,\n    maxCookTime: null,\n    minServings: null,\n    maxServings: null,\n    tags: []\n  })\n\n  const updateFilters = (newFilters: Partial<RecipeFilters>) => {\n    const updatedFilters = { ...filters, ...newFilters }\n    setFilters(updatedFilters)\n    onFiltersChange(updatedFilters)\n  }\n\n  const toggleArrayFilter = (key: keyof RecipeFilters, value: string) => {\n    const currentArray = filters[key] as string[]\n    const newArray = currentArray.includes(value)\n      ? currentArray.filter(item => item !== value)\n      : [...currentArray, value]\n    updateFilters({ [key]: newArray })\n  }\n\n  const clearAllFilters = () => {\n    const clearedFilters: RecipeFilters = {\n      search: '',\n      difficulty: [],\n      cuisine: [],\n      dietaryRestrictions: [],\n      maxPrepTime: null,\n      maxCookTime: null,\n      minServings: null,\n      maxServings: null,\n      tags: []\n    }\n    setFilters(clearedFilters)\n    onFiltersChange(clearedFilters)\n  }\n\n  const hasActiveFilters = Object.values(filters).some(value => \n    Array.isArray(value) ? value.length > 0 : value !== null && value !== ''\n  )\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border p-4 mb-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"flex items-center space-x-2\">\n            <FilterIcon className=\"h-5 w-5 text-gray-600\" />\n            <h3 className=\"font-medium text-gray-900\">Filters</h3>\n          </div>\n          <div className=\"text-sm text-gray-500\">\n            {filteredCount} of {totalRecipes} recipes\n          </div>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          {hasActiveFilters && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={clearAllFilters}\n              className=\"text-xs\"\n            >\n              Clear All\n            </Button>\n          )}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => setIsExpanded(!isExpanded)}\n          >\n            {isExpanded ? 'Less' : 'More'} Filters\n          </Button>\n        </div>\n      </div>\n\n      {/* Search */}\n      <div className=\"relative mb-4\">\n        <SearchIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n        <input\n          type=\"text\"\n          placeholder=\"Search recipes...\"\n          value={filters.search}\n          onChange={(e) => updateFilters({ search: e.target.value })}\n          className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n        />\n      </div>\n\n      {/* Quick Dietary Filters */}\n      <div className=\"mb-4\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Dietary Preferences\n        </label>\n        <div className=\"flex flex-wrap gap-2\">\n          {dietaryOptions.map((option) => (\n            <button\n              key={option.value}\n              onClick={() => toggleArrayFilter('dietaryRestrictions', option.value)}\n              className={`inline-flex items-center px-3 py-1 rounded-full text-sm transition-all duration-200 ${\n                filters.dietaryRestrictions.includes(option.value)\n                  ? 'bg-blue-100 text-blue-800 border border-blue-300'\n                  : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'\n              }`}\n            >\n              <span className=\"mr-1\">{option.icon}</span>\n              {option.label}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Expanded Filters */}\n      <AnimatePresence>\n        {isExpanded && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"space-y-4\"\n          >\n            {/* Difficulty */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Difficulty Level\n              </label>\n              <div className=\"flex flex-wrap gap-2\">\n                {difficultyOptions.map((option) => (\n                  <button\n                    key={option.value}\n                    onClick={() => toggleArrayFilter('difficulty', option.value)}\n                    className={`px-3 py-1 rounded-full text-sm transition-all duration-200 ${\n                      filters.difficulty.includes(option.value)\n                        ? `${option.color} border border-current`\n                        : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'\n                    }`}\n                  >\n                    {option.label}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Cuisine */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Cuisine Type\n              </label>\n              <div className=\"flex flex-wrap gap-2\">\n                {cuisineOptions.map((cuisine) => (\n                  <button\n                    key={cuisine}\n                    onClick={() => toggleArrayFilter('cuisine', cuisine)}\n                    className={`px-3 py-1 rounded-full text-sm transition-all duration-200 ${\n                      filters.cuisine.includes(cuisine)\n                        ? 'bg-purple-100 text-purple-800 border border-purple-300'\n                        : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'\n                    }`}\n                  >\n                    {cuisine}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Time and Servings */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  <ClockIcon className=\"inline h-4 w-4 mr-1\" />\n                  Max Prep Time (minutes)\n                </label>\n                <input\n                  type=\"number\"\n                  placeholder=\"e.g., 30\"\n                  value={filters.maxPrepTime || ''}\n                  onChange={(e) => updateFilters({ \n                    maxPrepTime: e.target.value ? parseInt(e.target.value) : null \n                  })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  <ClockIcon className=\"inline h-4 w-4 mr-1\" />\n                  Max Cook Time (minutes)\n                </label>\n                <input\n                  type=\"number\"\n                  placeholder=\"e.g., 60\"\n                  value={filters.maxCookTime || ''}\n                  onChange={(e) => updateFilters({ \n                    maxCookTime: e.target.value ? parseInt(e.target.value) : null \n                  })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  <UsersIcon className=\"inline h-4 w-4 mr-1\" />\n                  Min Servings\n                </label>\n                <input\n                  type=\"number\"\n                  placeholder=\"e.g., 2\"\n                  value={filters.minServings || ''}\n                  onChange={(e) => updateFilters({ \n                    minServings: e.target.value ? parseInt(e.target.value) : null \n                  })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  <UsersIcon className=\"inline h-4 w-4 mr-1\" />\n                  Max Servings\n                </label>\n                <input\n                  type=\"number\"\n                  placeholder=\"e.g., 8\"\n                  value={filters.maxServings || ''}\n                  onChange={(e) => updateFilters({ \n                    maxServings: e.target.value ? parseInt(e.target.value) : null \n                  })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Active Filters Display */}\n      {hasActiveFilters && (\n        <div className=\"mt-4 pt-4 border-t\">\n          <div className=\"flex flex-wrap gap-2\">\n            {filters.search && (\n              <Badge variant=\"outline\" className=\"flex items-center space-x-1\">\n                <span>Search: {filters.search}</span>\n                <button\n                  onClick={() => updateFilters({ search: '' })}\n                  className=\"ml-1 hover:text-red-600\"\n                >\n                  <XIcon className=\"h-3 w-3\" />\n                </button>\n              </Badge>\n            )}\n            {[...filters.difficulty, ...filters.cuisine, ...filters.dietaryRestrictions].map((filter) => (\n              <Badge key={filter} variant=\"outline\" className=\"flex items-center space-x-1\">\n                <span>{filter}</span>\n                <button\n                  onClick={() => {\n                    if (filters.difficulty.includes(filter)) toggleArrayFilter('difficulty', filter)\n                    if (filters.cuisine.includes(filter)) toggleArrayFilter('cuisine', filter)\n                    if (filters.dietaryRestrictions.includes(filter)) toggleArrayFilter('dietaryRestrictions', filter)\n                  }}\n                  className=\"ml-1 hover:text-red-600\"\n                >\n                  <XIcon className=\"h-3 w-3\" />\n                </button>\n              </Badge>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAkCA,MAAM,oBAAoB;IACxB;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAA8B;IACrE;QAAE,OAAO;QAAU,OAAO;QAAU,OAAO;IAAgC;IAC3E;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAA0B;CAClE;AAED,MAAM,iBAAiB;IACrB;IAAW;IAAW;IAAS;IAAY;IAC3C;IAAU;IAAU;IAAQ;IAAW;IAAY;IAAS;CAC7D;AAED,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAc,OAAO;QAAc,MAAM;IAAK;IACvD;QAAE,OAAO;QAAS,OAAO;QAAS,MAAM;IAAK;IAC7C;QAAE,OAAO;QAAe,OAAO;QAAe,MAAM;IAAK;IACzD;QAAE,OAAO;QAAc,OAAO;QAAc,MAAM;IAAK;IACvD;QAAE,OAAO;QAAQ,OAAO;QAAQ,MAAM;IAAK;IAC3C;QAAE,OAAO;QAAS,OAAO;QAAS,MAAM;IAAK;IAC7C;QAAE,OAAO;QAAY,OAAO;QAAY,MAAM;IAAK;IACnD;QAAE,OAAO;QAAgB,OAAO;QAAgB,MAAM;IAAK;CAC5D;AAEc,SAAS,eAAe,EACrC,eAAe,EACf,YAAY,EACZ,aAAa,EACO;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,QAAQ;QACR,YAAY,EAAE;QACd,SAAS,EAAE;QACX,qBAAqB,EAAE;QACvB,aAAa;QACb,aAAa;QACb,aAAa;QACb,aAAa;QACb,MAAM,EAAE;IACV;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,iBAAiB;YAAE,GAAG,OAAO;YAAE,GAAG,UAAU;QAAC;QACnD,WAAW;QACX,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,CAAC,KAA0B;QACnD,MAAM,eAAe,OAAO,CAAC,IAAI;QACjC,MAAM,WAAW,aAAa,QAAQ,CAAC,SACnC,aAAa,MAAM,CAAC,CAAA,OAAQ,SAAS,SACrC;eAAI;YAAc;SAAM;QAC5B,cAAc;YAAE,CAAC,IAAI,EAAE;QAAS;IAClC;IAEA,MAAM,kBAAkB;QACtB,MAAM,iBAAgC;YACpC,QAAQ;YACR,YAAY,EAAE;YACd,SAAS,EAAE;YACX,qBAAqB,EAAE;YACvB,aAAa;YACb,aAAa;YACb,aAAa;YACb,aAAa;YACb,MAAM,EAAE;QACV;QACA,WAAW;QACX,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,QACnD,MAAM,OAAO,CAAC,SAAS,MAAM,MAAM,GAAG,IAAI,UAAU,QAAQ,UAAU;IAGxE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;;;;;;;0CAE5C,8OAAC;gCAAI,WAAU;;oCACZ;oCAAc;oCAAK;oCAAa;;;;;;;;;;;;;kCAGrC,8OAAC;wBAAI,WAAU;;4BACZ,kCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAIH,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,cAAc,CAAC;;oCAE7B,aAAa,SAAS;oCAAO;;;;;;;;;;;;;;;;;;;0BAMpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;kCACtB,8OAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,OAAO,QAAQ,MAAM;wBACrB,UAAU,CAAC,IAAM,cAAc;gCAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACxD,WAAU;;;;;;;;;;;;0BAKd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,8OAAC;gCAEC,SAAS,IAAM,kBAAkB,uBAAuB,OAAO,KAAK;gCACpE,WAAW,CAAC,oFAAoF,EAC9F,QAAQ,mBAAmB,CAAC,QAAQ,CAAC,OAAO,KAAK,IAC7C,qDACA,sEACJ;;kDAEF,8OAAC;wCAAK,WAAU;kDAAQ,OAAO,IAAI;;;;;;oCAClC,OAAO,KAAK;;+BATR,OAAO,KAAK;;;;;;;;;;;;;;;;0BAgBzB,8OAAC,yLAAA,CAAA,kBAAe;0BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;;sCAGV,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAC,uBACtB,8OAAC;4CAEC,SAAS,IAAM,kBAAkB,cAAc,OAAO,KAAK;4CAC3D,WAAW,CAAC,2DAA2D,EACrE,QAAQ,UAAU,CAAC,QAAQ,CAAC,OAAO,KAAK,IACpC,GAAG,OAAO,KAAK,CAAC,sBAAsB,CAAC,GACvC,sEACJ;sDAED,OAAO,KAAK;2CARR,OAAO,KAAK;;;;;;;;;;;;;;;;sCAezB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;4CAEC,SAAS,IAAM,kBAAkB,WAAW;4CAC5C,WAAW,CAAC,2DAA2D,EACrE,QAAQ,OAAO,CAAC,QAAQ,CAAC,WACrB,2DACA,sEACJ;sDAED;2CARI;;;;;;;;;;;;;;;;sCAeb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC,wMAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;sDAG/C,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,QAAQ,WAAW,IAAI;4CAC9B,UAAU,CAAC,IAAM,cAAc;oDAC7B,aAAa,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gDAC3D;4CACA,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC,wMAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;sDAG/C,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,QAAQ,WAAW,IAAI;4CAC9B,UAAU,CAAC,IAAM,cAAc;oDAC7B,aAAa,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gDAC3D;4CACA,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC,wMAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;sDAG/C,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,QAAQ,WAAW,IAAI;4CAC9B,UAAU,CAAC,IAAM,cAAc;oDAC7B,aAAa,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gDAC3D;4CACA,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC,wMAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;sDAG/C,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,QAAQ,WAAW,IAAI;4CAC9B,UAAU,CAAC,IAAM,cAAc;oDAC7B,aAAa,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gDAC3D;4CACA,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASrB,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,QAAQ,MAAM,kBACb,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;;8CACjC,8OAAC;;wCAAK;wCAAS,QAAQ,MAAM;;;;;;;8CAC7B,8OAAC;oCACC,SAAS,IAAM,cAAc;4CAAE,QAAQ;wCAAG;oCAC1C,WAAU;8CAEV,cAAA,8OAAC,gMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAItB;+BAAI,QAAQ,UAAU;+BAAK,QAAQ,OAAO;+BAAK,QAAQ,mBAAmB;yBAAC,CAAC,GAAG,CAAC,CAAC,uBAChF,8OAAC,iIAAA,CAAA,QAAK;gCAAc,SAAQ;gCAAU,WAAU;;kDAC9C,8OAAC;kDAAM;;;;;;kDACP,8OAAC;wCACC,SAAS;4CACP,IAAI,QAAQ,UAAU,CAAC,QAAQ,CAAC,SAAS,kBAAkB,cAAc;4CACzE,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,SAAS,kBAAkB,WAAW;4CACnE,IAAI,QAAQ,mBAAmB,CAAC,QAAQ,CAAC,SAAS,kBAAkB,uBAAuB;wCAC7F;wCACA,WAAU;kDAEV,cAAA,8OAAC,gMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;+BAVT;;;;;;;;;;;;;;;;;;;;;;AAmB1B", "debugId": null}}, {"offset": {"line": 1720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n  helperText?: string\n  label?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, helperText, label, id, ...props }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`\n    const helperTextId = helperText ? `${inputId}-helper` : undefined\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n            {props.required && <span className=\"text-red-500 ml-1\" aria-label=\"required\">*</span>}\n          </label>\n        )}\n        <input\n          id={inputId}\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 hover:border-gray-400',\n            error && 'border-red-500 focus-visible:ring-red-500 aria-invalid:border-red-500',\n            className\n          )}\n          ref={ref}\n          aria-invalid={error}\n          aria-describedby={helperTextId}\n          {...props}\n        />\n        {helperText && (\n          <p\n            id={helperTextId}\n            className={cn(\n              'mt-1 text-xs',\n              error ? 'text-red-600' : 'text-gray-500'\n            )}\n            role={error ? 'alert' : 'status'}\n          >\n            {helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE,MAAM,eAAe,aAAa,GAAG,QAAQ,OAAO,CAAC,GAAG;IAExD,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,8OAAC;wBAAK,WAAU;wBAAoB,cAAW;kCAAW;;;;;;;;;;;;0BAGjF,8OAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA,SAAS,yEACT;gBAEF,KAAK;gBACL,gBAAc;gBACd,oBAAkB;gBACjB,GAAG,KAAK;;;;;;YAEV,4BACC,8OAAC;gBACC,IAAI;gBACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBACA,QAAQ,iBAAiB;gBAE3B,MAAM,QAAQ,UAAU;0BAEvB;;;;;;;;;;;;AAKX;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1793, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/recipes/MealPlanModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Modal } from '@/components/ui/Modal'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { XIcon, CalendarIcon, ClockIcon } from 'lucide-react'\nimport { format, addDays } from 'date-fns'\n\ninterface MealPlanModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onSubmit: (data: MealPlanData) => Promise<void>\n  recipeTitle: string\n  defaultServings?: number\n}\n\ninterface MealPlanData {\n  recipeId: string\n  plannedDate: string\n  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack'\n  servings: number\n}\n\nconst mealTypes = [\n  { value: 'breakfast', label: 'Breakfast', icon: '🌅' },\n  { value: 'lunch', label: 'Lunch', icon: '☀️' },\n  { value: 'dinner', label: 'Dinner', icon: '🌙' },\n  { value: 'snack', label: 'Snack', icon: '🍎' },\n]\n\nconst quickDateOptions = [\n  { label: 'Today', date: new Date() },\n  { label: 'Tomorrow', date: addDays(new Date(), 1) },\n  { label: 'This Weekend', date: addDays(new Date(), 6 - new Date().getDay()) },\n  { label: 'Next Week', date: addDays(new Date(), 7) },\n]\n\nexport default function MealPlanModal({\n  isOpen,\n  onClose,\n  onSubmit,\n  recipeTitle,\n  defaultServings = 1\n}: MealPlanModalProps) {\n  const [formData, setFormData] = useState({\n    plannedDate: format(new Date(), 'yyyy-MM-dd'),\n    mealType: 'dinner' as 'breakfast' | 'lunch' | 'dinner' | 'snack',\n    servings: defaultServings\n  })\n  const [isSubmitting, setIsSubmitting] = useState(false)\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsSubmitting(true)\n    \n    try {\n      await onSubmit({\n        recipeId: '', // This will be set by the parent component\n        ...formData\n      })\n      onClose()\n    } catch (error) {\n      console.error('Error adding to meal plan:', error)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const handleQuickDate = (date: Date) => {\n    setFormData(prev => ({\n      ...prev,\n      plannedDate: format(date, 'yyyy-MM-dd')\n    }))\n  }\n\n  return (\n    <Modal isOpen={isOpen} onClose={onClose} size=\"md\">\n      <motion.div\n        initial={{ opacity: 0, scale: 0.95 }}\n        animate={{ opacity: 1, scale: 1 }}\n        exit={{ opacity: 0, scale: 0.95 }}\n        className=\"bg-white rounded-lg shadow-xl max-w-md w-full mx-4\"\n      >\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">Add to Meal Plan</h3>\n            <p className=\"text-sm text-gray-600 mt-1\">{recipeTitle}</p>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <XIcon className=\"h-5 w-5\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n          {/* Quick Date Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Quick Select\n            </label>\n            <div className=\"grid grid-cols-2 gap-2\">\n              {quickDateOptions.map((option) => (\n                <button\n                  key={option.label}\n                  type=\"button\"\n                  onClick={() => handleQuickDate(option.date)}\n                  className=\"p-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n                >\n                  {option.label}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Date Selection */}\n          <div>\n            <Input\n              label=\"Planned Date\"\n              type=\"date\"\n              value={formData.plannedDate}\n              onChange={(e) => setFormData(prev => ({ ...prev, plannedDate: e.target.value }))}\n              required\n            />\n          </div>\n\n          {/* Meal Type Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Meal Type\n            </label>\n            <div className=\"grid grid-cols-2 gap-2\">\n              {mealTypes.map((type) => (\n                <button\n                  key={type.value}\n                  type=\"button\"\n                  onClick={() => setFormData(prev => ({ ...prev, mealType: type.value as any }))}\n                  className={`p-3 border rounded-lg transition-all duration-200 ${\n                    formData.mealType === type.value\n                      ? 'border-blue-500 bg-blue-50 text-blue-700'\n                      : 'border-gray-300 hover:bg-gray-50'\n                  }`}\n                >\n                  <div className=\"text-lg mb-1\">{type.icon}</div>\n                  <div className=\"text-sm font-medium\">{type.label}</div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Servings */}\n          <div>\n            <Input\n              label=\"Number of Servings\"\n              type=\"number\"\n              min=\"1\"\n              max=\"20\"\n              value={formData.servings}\n              onChange={(e) => setFormData(prev => ({ ...prev, servings: parseInt(e.target.value) || 1 }))}\n              required\n            />\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex space-x-3 pt-4\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={onClose}\n              className=\"flex-1\"\n              disabled={isSubmitting}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"submit\"\n              className=\"flex-1\"\n              disabled={isSubmitting}\n            >\n              {isSubmitting ? 'Adding...' : 'Add to Plan'}\n            </Button>\n          </div>\n        </form>\n      </motion.div>\n    </Modal>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AARA;;;;;;;;;AAyBA,MAAM,YAAY;IAChB;QAAE,OAAO;QAAa,OAAO;QAAa,MAAM;IAAK;IACrD;QAAE,OAAO;QAAS,OAAO;QAAS,MAAM;IAAK;IAC7C;QAAE,OAAO;QAAU,OAAO;QAAU,MAAM;IAAK;IAC/C;QAAE,OAAO;QAAS,OAAO;QAAS,MAAM;IAAK;CAC9C;AAED,MAAM,mBAAmB;IACvB;QAAE,OAAO;QAAS,MAAM,IAAI;IAAO;IACnC;QAAE,OAAO;QAAY,MAAM,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;IAAG;IAClD;QAAE,OAAO;QAAgB,MAAM,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ,IAAI,IAAI,OAAO,MAAM;IAAI;IAC5E;QAAE,OAAO;QAAa,MAAM,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;IAAG;CACpD;AAEc,SAAS,cAAc,EACpC,MAAM,EACN,OAAO,EACP,QAAQ,EACR,WAAW,EACX,kBAAkB,CAAC,EACA;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,aAAa,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;QAChC,UAAU;QACV,UAAU;IACZ;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,IAAI;YACF,MAAM,SAAS;gBACb,UAAU;gBACV,GAAG,QAAQ;YACb;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,aAAa,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;YAC5B,CAAC;IACH;IAEA,qBACE,8OAAC,iIAAA,CAAA,QAAK;QAAC,QAAQ;QAAQ,SAAS;QAAS,MAAK;kBAC5C,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAK;YACnC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAK;YAChC,WAAU;;8BAEV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;sCAE7C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIrB,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,uBACrB,8OAAC;4CAEC,MAAK;4CACL,SAAS,IAAM,gBAAgB,OAAO,IAAI;4CAC1C,WAAU;sDAET,OAAO,KAAK;2CALR,OAAO,KAAK;;;;;;;;;;;;;;;;sCAYzB,8OAAC;sCACC,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCACJ,OAAM;gCACN,MAAK;gCACL,OAAO,SAAS,WAAW;gCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAC9E,QAAQ;;;;;;;;;;;sCAKZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;4CAEC,MAAK;4CACL,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,UAAU,KAAK,KAAK;oDAAQ,CAAC;4CAC5E,WAAW,CAAC,kDAAkD,EAC5D,SAAS,QAAQ,KAAK,KAAK,KAAK,GAC5B,6CACA,oCACJ;;8DAEF,8OAAC;oDAAI,WAAU;8DAAgB,KAAK,IAAI;;;;;;8DACxC,8OAAC;oDAAI,WAAU;8DAAuB,KAAK,KAAK;;;;;;;2CAV3C,KAAK,KAAK;;;;;;;;;;;;;;;;sCAiBvB,8OAAC;sCACC,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCACJ,OAAM;gCACN,MAAK;gCACL,KAAI;gCACJ,KAAI;gCACJ,OAAO,SAAS,QAAQ;gCACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wCAAE,CAAC;gCAC1F,QAAQ;;;;;;;;;;;sCAKZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;oCACV,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,UAAU;8CAET,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 2136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/recipes/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useMemo } from 'react'\nimport { recipesService, type Recipe } from '@/lib/services/recipes-service'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { PlusIcon, LinkIcon, ChefHatIcon, CalendarIcon, BarChart3Icon, GridIcon, ListIcon } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\nimport { Modal } from '@/components/ui/Modal'\nimport EnhancedRecipeCard from '@/components/recipes/EnhancedRecipeCard'\nimport DietaryFilters, { RecipeFilters } from '@/components/recipes/DietaryFilters'\nimport MealPlanModal from '@/components/recipes/MealPlanModal'\nimport toast from 'react-hot-toast'\n\nexport default function RecipesPage() {\n  const [recipes, setRecipes] = useState<Recipe[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddForm, setShowAddForm] = useState(false)\n  const [showImportForm, setShowImportForm] = useState(false)\n  const [showMealPlanModal, setShowMealPlanModal] = useState(false)\n  const [selectedRecipeForMealPlan, setSelectedRecipeForMealPlan] = useState<Recipe | null>(null)\n  const [importUrl, setImportUrl] = useState('')\n  const [importing, setImporting] = useState(false)\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')\n  const [filters, setFilters] = useState<RecipeFilters>({\n    search: '',\n    difficulty: [],\n    cuisine: [],\n    dietaryRestrictions: [],\n    maxPrepTime: null,\n    maxCookTime: null,\n    minServings: null,\n    maxServings: null,\n    tags: []\n  })\n  const [newRecipe, setNewRecipe] = useState({\n    title: '',\n    description: '',\n    prep_time: '',\n    cook_time: '',\n    servings: '',\n    difficulty: 'medium' as 'easy' | 'medium' | 'hard',\n    cuisine: '',\n    ingredients: [''],\n    instructions: [''],\n  })\n\n  useEffect(() => {\n    fetchRecipes()\n\n    // Set up polling for real-time updates (secure alternative to direct Supabase subscriptions)\n    const pollInterval = setInterval(() => {\n      fetchRecipes()\n    }, 5000) // Poll every 5 seconds\n\n    return () => {\n      clearInterval(pollInterval)\n    }\n  }, [])\n\n  // Filter recipes based on current filters\n  const filteredRecipes = useMemo(() => {\n    return recipes.filter(recipe => {\n      // Search filter\n      if (filters.search && !recipe.title.toLowerCase().includes(filters.search.toLowerCase()) &&\n          !recipe.description?.toLowerCase().includes(filters.search.toLowerCase())) {\n        return false\n      }\n\n      // Difficulty filter\n      if (filters.difficulty.length > 0 && !filters.difficulty.includes(recipe.difficulty)) {\n        return false\n      }\n\n      // Cuisine filter\n      if (filters.cuisine.length > 0 && (!recipe.cuisine || !filters.cuisine.includes(recipe.cuisine))) {\n        return false\n      }\n\n      // Time filters\n      if (filters.maxPrepTime && recipe.prep_time && recipe.prep_time > filters.maxPrepTime) {\n        return false\n      }\n      if (filters.maxCookTime && recipe.cook_time && recipe.cook_time > filters.maxCookTime) {\n        return false\n      }\n\n      // Servings filters\n      if (filters.minServings && recipe.servings && recipe.servings < filters.minServings) {\n        return false\n      }\n      if (filters.maxServings && recipe.servings && recipe.servings > filters.maxServings) {\n        return false\n      }\n\n      // Dietary restrictions filter (check tags)\n      if (filters.dietaryRestrictions.length > 0) {\n        const recipeTags = recipe.tags || []\n        const hasMatchingDiet = filters.dietaryRestrictions.some(diet => \n          recipeTags.some(tag => tag.toLowerCase().includes(diet.toLowerCase()))\n        )\n        if (!hasMatchingDiet) return false\n      }\n\n      return true\n    })\n  }, [recipes, filters])\n\n  const fetchRecipes = async () => {\n    try {\n      const data = await recipesService.getRecipes()\n      setRecipes(data || [])\n    } catch (error) {\n      console.error('Error fetching recipes:', error)\n      toast.error('Failed to load recipes')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleAddToMealPlan = (recipeId: string) => {\n    const recipe = recipes.find(r => r.id === recipeId)\n    if (recipe) {\n      setSelectedRecipeForMealPlan(recipe)\n      setShowMealPlanModal(true)\n    }\n  }\n\n  const handleMealPlanSubmit = async (mealPlanData: any) => {\n    if (!selectedRecipeForMealPlan) return\n\n    try {\n      const response = await fetch('/api/meal-plans', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          recipeId: selectedRecipeForMealPlan.id,\n          ...mealPlanData\n        })\n      })\n\n      if (!response.ok) throw new Error('Failed to add to meal plan')\n\n      toast.success('Added to meal plan!')\n      setShowMealPlanModal(false)\n      setSelectedRecipeForMealPlan(null)\n    } catch (error) {\n      console.error('Error adding to meal plan:', error)\n      toast.error('Failed to add to meal plan')\n    }\n  }\n\n  const handleAddToShoppingList = async (recipeId: string) => {\n    try {\n      const response = await fetch(`/api/recipes/${recipeId}/add-to-shopping-list`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ servings: 1 })\n      })\n\n      if (!response.ok) throw new Error('Failed to add to shopping list')\n\n      const result = await response.json()\n      toast.success(`Added ${result.addedItems} ingredients to shopping list!`)\n    } catch (error) {\n      console.error('Error adding to shopping list:', error)\n      toast.error('Failed to add ingredients to shopping list')\n    }\n  }\n\n  const handleAnalyzeNutrition = async (recipeId: string) => {\n    try {\n      const response = await fetch(`/api/recipes/${recipeId}/nutrition`, {\n        method: 'POST'\n      })\n\n      if (!response.ok) throw new Error('Failed to analyze nutrition')\n\n      // Refresh recipes to show updated nutritional info\n      await fetchRecipes()\n      toast.success('Nutritional analysis completed!')\n    } catch (error) {\n      console.error('Error analyzing nutrition:', error)\n      toast.error('Failed to analyze nutrition')\n    }\n  }\n\n  const importRecipe = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!importUrl.trim()) return\n\n    setImporting(true)\n    try {\n      const recipe = await recipesService.importRecipeFromUrl(importUrl)\n\n      setImportUrl('')\n      setShowImportForm(false)\n      toast.success('Recipe imported successfully!')\n      fetchRecipes()\n    } catch (error) {\n      console.error('Error importing recipe:', error)\n      toast.error('Failed to import recipe. Please check the URL and try again.')\n    } finally {\n      setImporting(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\"\n      >\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Recipe Collection</h1>\n          <p className=\"mt-2 text-gray-600\">\n            Discover, save, and organize your favorite recipes with advanced features\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-3\">\n          {/* View Mode Toggle */}\n          <div className=\"flex items-center bg-gray-100 rounded-lg p-1\">\n            <Button\n              variant={viewMode === 'grid' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('grid')}\n              className=\"h-8\"\n            >\n              <GridIcon className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant={viewMode === 'list' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('list')}\n              className=\"h-8\"\n            >\n              <ListIcon className=\"h-4 w-4\" />\n            </Button>\n          </div>\n\n          <Button\n            onClick={() => setShowImportForm(true)}\n            variant=\"outline\"\n          >\n            <LinkIcon className=\"h-4 w-4 mr-2\" />\n            Import Recipe\n          </Button>\n          <Button onClick={() => setShowAddForm(true)}>\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            Add Recipe\n          </Button>\n        </div>\n      </motion.div>\n\n      {/* Filters */}\n      <DietaryFilters\n        onFiltersChange={setFilters}\n        totalRecipes={recipes.length}\n        filteredCount={filteredRecipes.length}\n      />\n\n      {/* Import Recipe Form */}\n      <Modal isOpen={showImportForm} onClose={() => setShowImportForm(false)} size=\"md\">\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Import Recipe from URL</h3>\n          <form onSubmit={importRecipe} className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"importUrl\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Recipe URL\n              </label>\n              <input\n                type=\"url\"\n                id=\"importUrl\"\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                value={importUrl}\n                onChange={(e) => setImportUrl(e.target.value)}\n                placeholder=\"https://example.com/recipe\"\n              />\n            </div>\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => setShowImportForm(false)}\n                disabled={importing}\n              >\n                Cancel\n              </Button>\n              <Button type=\"submit\" disabled={importing}>\n                {importing ? 'Importing...' : 'Import Recipe'}\n              </Button>\n            </div>\n          </form>\n        </div>\n      </Modal>\n\n      {/* Recipes Grid/List */}\n      <AnimatePresence mode=\"wait\">\n        {filteredRecipes.length === 0 ? (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"text-center py-12\"\n          >\n            <ChefHatIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">\n              {recipes.length === 0 ? 'No recipes yet' : 'No recipes match your filters'}\n            </h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {recipes.length === 0 \n                ? 'Get started by adding your first recipe or importing from a URL.'\n                : 'Try adjusting your search criteria or filters.'\n              }\n            </p>\n          </motion.div>\n        ) : (\n          <motion.div\n            key={viewMode}\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className={viewMode === 'grid' \n              ? 'grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'\n              : 'space-y-4'\n            }\n          >\n            {filteredRecipes.map((recipe, index) => (\n              <motion.div\n                key={recipe.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.05 }}\n              >\n                <EnhancedRecipeCard\n                  recipe={recipe}\n                  onAddToMealPlan={handleAddToMealPlan}\n                  onAddToShoppingList={handleAddToShoppingList}\n                  onAnalyzeNutrition={handleAnalyzeNutrition}\n                />\n              </motion.div>\n            ))}\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Meal Plan Modal */}\n      {selectedRecipeForMealPlan && (\n        <MealPlanModal\n          isOpen={showMealPlanModal}\n          onClose={() => {\n            setShowMealPlanModal(false)\n            setSelectedRecipeForMealPlan(null)\n          }}\n          onSubmit={handleMealPlanSubmit}\n          recipeTitle={selectedRecipeForMealPlan.title}\n          defaultServings={selectedRecipeForMealPlan.servings || 1}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,QAAQ;QACR,YAAY,EAAE;QACd,SAAS,EAAE;QACX,qBAAqB,EAAE;QACvB,aAAa;QACb,aAAa;QACb,aAAa;QACb,aAAa;QACb,MAAM,EAAE;IACV;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,OAAO;QACP,aAAa;QACb,WAAW;QACX,WAAW;QACX,UAAU;QACV,YAAY;QACZ,SAAS;QACT,aAAa;YAAC;SAAG;QACjB,cAAc;YAAC;SAAG;IACpB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,6FAA6F;QAC7F,MAAM,eAAe,YAAY;YAC/B;QACF,GAAG,MAAM,uBAAuB;;QAEhC,OAAO;YACL,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,0CAA0C;IAC1C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,OAAO,QAAQ,MAAM,CAAC,CAAA;YACpB,gBAAgB;YAChB,IAAI,QAAQ,MAAM,IAAI,CAAC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,MAAM,CAAC,WAAW,OACjF,CAAC,OAAO,WAAW,EAAE,cAAc,SAAS,QAAQ,MAAM,CAAC,WAAW,KAAK;gBAC7E,OAAO;YACT;YAEA,oBAAoB;YACpB,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,UAAU,CAAC,QAAQ,CAAC,OAAO,UAAU,GAAG;gBACpF,OAAO;YACT;YAEA,iBAAiB;YACjB,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,OAAO,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,OAAO,OAAO,CAAC,GAAG;gBAChG,OAAO;YACT;YAEA,eAAe;YACf,IAAI,QAAQ,WAAW,IAAI,OAAO,SAAS,IAAI,OAAO,SAAS,GAAG,QAAQ,WAAW,EAAE;gBACrF,OAAO;YACT;YACA,IAAI,QAAQ,WAAW,IAAI,OAAO,SAAS,IAAI,OAAO,SAAS,GAAG,QAAQ,WAAW,EAAE;gBACrF,OAAO;YACT;YAEA,mBAAmB;YACnB,IAAI,QAAQ,WAAW,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,GAAG,QAAQ,WAAW,EAAE;gBACnF,OAAO;YACT;YACA,IAAI,QAAQ,WAAW,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,GAAG,QAAQ,WAAW,EAAE;gBACnF,OAAO;YACT;YAEA,2CAA2C;YAC3C,IAAI,QAAQ,mBAAmB,CAAC,MAAM,GAAG,GAAG;gBAC1C,MAAM,aAAa,OAAO,IAAI,IAAI,EAAE;gBACpC,MAAM,kBAAkB,QAAQ,mBAAmB,CAAC,IAAI,CAAC,CAAA,OACvD,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,KAAK,WAAW;gBAEpE,IAAI,CAAC,iBAAiB,OAAO;YAC/B;YAEA,OAAO;QACT;IACF,GAAG;QAAC;QAAS;KAAQ;IAErB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,OAAO,MAAM,4IAAA,CAAA,iBAAc,CAAC,UAAU;YAC5C,WAAW,QAAQ,EAAE;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,QAAQ;YACV,6BAA6B;YAC7B,qBAAqB;QACvB;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,2BAA2B;QAEhC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,0BAA0B,EAAE;oBACtC,GAAG,YAAY;gBACjB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,qBAAqB;YACrB,6BAA6B;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,SAAS,qBAAqB,CAAC,EAAE;gBAC5E,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,UAAU;gBAAE;YACrC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,UAAU,CAAC,8BAA8B,CAAC;QAC1E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,SAAS,UAAU,CAAC,EAAE;gBACjE,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,mDAAmD;YACnD,MAAM;YACN,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,UAAU,IAAI,IAAI;QAEvB,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,4IAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC;YAExD,aAAa;YACb,kBAAkB;YAClB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,8OAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,8OAAC,sMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIxB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,kBAAkB;gCACjC,SAAQ;;kDAER,8OAAC,sMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,eAAe;;kDACpC,8OAAC,sMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC,+IAAA,CAAA,UAAc;gBACb,iBAAiB;gBACjB,cAAc,QAAQ,MAAM;gBAC5B,eAAe,gBAAgB,MAAM;;;;;;0BAIvC,8OAAC,iIAAA,CAAA,QAAK;gBAAC,QAAQ;gBAAgB,SAAS,IAAM,kBAAkB;gBAAQ,MAAK;0BAC3E,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAY,WAAU;sDAA+C;;;;;;sDAGpF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,QAAQ;4CACR,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC5C,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,kBAAkB;4CACjC,UAAU;sDACX;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,UAAU;sDAC7B,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC,yLAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,WAAU;;sCAEV,8OAAC,gNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAG,WAAU;sCACX,QAAQ,MAAM,KAAK,IAAI,mBAAmB;;;;;;sCAE7C,8OAAC;4BAAE,WAAU;sCACV,QAAQ,MAAM,KAAK,IAChB,qEACA;;;;;;;;;;;yCAKR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,WAAW,aAAa,SACpB,wEACA;8BAGH,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO,QAAQ;4BAAK;sCAElC,cAAA,8OAAC,mJAAA,CAAA,UAAkB;gCACjB,QAAQ;gCACR,iBAAiB;gCACjB,qBAAqB;gCACrB,oBAAoB;;;;;;2BATjB,OAAO,EAAE;;;;;mBAVb;;;;;;;;;;YA4BV,2CACC,8OAAC,8IAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,SAAS;oBACP,qBAAqB;oBACrB,6BAA6B;gBAC/B;gBACA,UAAU;gBACV,aAAa,0BAA0B,KAAK;gBAC5C,iBAAiB,0BAA0B,QAAQ,IAAI;;;;;;;;;;;;AAKjE", "debugId": null}}]}