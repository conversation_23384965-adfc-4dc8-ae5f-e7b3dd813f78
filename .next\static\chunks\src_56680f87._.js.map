{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { cva, type VariantProps } from 'class-variance-authority'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed ring-offset-background transform hover:scale-105 active:scale-95 focus:scale-105',\n  {\n    variants: {\n      variant: {\n        default: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl focus-visible:ring-blue-500',\n        destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl focus-visible:ring-red-500',\n        outline: 'border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus-visible:ring-gray-500',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 focus-visible:ring-gray-500',\n        ghost: 'hover:bg-gray-100 text-gray-700 focus-visible:ring-gray-500',\n        link: 'underline-offset-4 hover:underline text-blue-600 focus-visible:ring-blue-500',\n        success: 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl focus-visible:ring-green-500',\n        warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 shadow-lg hover:shadow-xl focus-visible:ring-yellow-500',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 text-xs',\n        lg: 'h-12 px-8 text-base',\n        xl: 'h-14 px-10 text-lg',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n  loadingText?: string\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, loadingText, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        aria-disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            {loadingText || 'Loading...'}\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,gVACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjF,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACtB,iBAAe,YAAY;QAC1B,GAAG,KAAK;kBAER,wBACC;;8BACE,6LAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,eAAY;;sCAEZ,6LAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAGL,eAAe;;2BAGlB;;;;;;AAIR;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Loading.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'spinner' | 'dots' | 'pulse'\n  className?: string\n  text?: string\n}\n\nconst sizeClasses = {\n  sm: 'h-4 w-4',\n  md: 'h-6 w-6',\n  lg: 'h-8 w-8',\n  xl: 'h-12 w-12',\n}\n\nexport function Loading({ \n  size = 'md', \n  variant = 'spinner', \n  className,\n  text \n}: LoadingProps) {\n  if (variant === 'spinner') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'dots') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div className=\"flex space-x-1\">\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.3s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.15s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce\"></div>\n        </div>\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'pulse') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'bg-blue-600 rounded-full animate-pulse',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  return null\n}\n\nexport function LoadingSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn('animate-pulse', className)}>\n      <div className=\"bg-gray-200 rounded-lg h-4 w-full\"></div>\n    </div>\n  )\n}\n\nexport function LoadingCard() {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-6 animate-pulse\">\n      <div className=\"space-y-4\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n        <div className=\"space-y-2\">\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\"></div>\n        </div>\n        <div className=\"h-8 bg-gray-200 rounded w-1/4\"></div>\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingTable({ rows = 5 }: { rows?: number }) {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>\n      </div>\n      <div className=\"divide-y divide-gray-200\">\n        {Array.from({ length: rows }).map((_, i) => (\n          <div key={i} className=\"p-4 animate-pulse\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"h-4 w-4 bg-gray-200 rounded\"></div>\n              <div className=\"flex-1 space-y-2\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n              </div>\n              <div className=\"h-8 w-8 bg-gray-200 rounded\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingStats() {\n  return (\n    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n      {Array.from({ length: 4 }).map((_, i) => (\n        <div key={i} className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-4 animate-pulse\">\n          <div className=\"text-center space-y-2\">\n            <div className=\"h-8 bg-gray-200 rounded w-16 mx-auto\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-20 mx-auto\"></div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AASA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,QAAQ,EACtB,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,SAAS,EACT,IAAI,EACS;IACb,IAAI,YAAY,WAAW;QACzB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,QAAQ;QACtB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;gBAEhB,sBAAQ,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,SAAS;QACvB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,OAAO;AACT;KAhDgB;AAkDT,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IACnE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBAClC,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;MANgB;AAQT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;MAbgB;AAeT,SAAS,aAAa,EAAE,OAAO,CAAC,EAAqB;IAC1D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAEjB,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,6LAAC;wBAAY,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;;;;;;;;;;;uBAPT;;;;;;;;;;;;;;;;AAcpB;MAtBgB;AAwBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gBAAY,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;eAHT;;;;;;;;;;AASlB;MAbgB", "debugId": null}}, {"offset": {"line": 474, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/chat/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { chatService, type ChatConversation, type ChatMessage } from '@/lib/services/chat-service'\nimport { SendIcon, MessageCircleIcon, PlusIcon, BotIcon, UserIcon, TrashIcon, EditIcon, SparklesIcon, TrendingUpIcon, CheckCircleIcon, ShoppingCartIcon, DollarSignIcon } from 'lucide-react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { Badge } from '@/components/ui/Badge'\nimport { Input } from '@/components/ui/Input'\nimport { Loading } from '@/components/ui/Loading'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport toast from 'react-hot-toast'\n\n// Types are now imported from the chat service\n\nexport default function ChatPage() {\n  const [conversations, setConversations] = useState<ChatConversation[]>([])\n  const [currentConversation, setCurrentConversation] = useState<string | null>(null)\n  const [messages, setMessages] = useState<ChatMessage[]>([])\n  const [newMessage, setNewMessage] = useState('')\n  const [loading, setLoading] = useState(true)\n  const [sending, setSending] = useState(false)\n  const [editingConversation, setEditingConversation] = useState<string | null>(null)\n  const [editingTitle, setEditingTitle] = useState('')\n  const [showSuggestions, setShowSuggestions] = useState(true)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  const quickSuggestions = [\n    { icon: CheckCircleIcon, text: \"What tasks should I prioritize today?\", color: \"text-blue-600\" },\n    { icon: DollarSignIcon, text: \"How is my budget looking this month?\", color: \"text-green-600\" },\n    { icon: ShoppingCartIcon, text: \"Help me create a grocery list\", color: \"text-purple-600\" },\n    { icon: TrendingUpIcon, text: \"Give me insights on my spending\", color: \"text-orange-600\" },\n  ]\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    fetchConversations()\n\n    // Set up real-time subscriptions\n    const messagesSubscription = supabase\n      .channel('chat_messages_changes')\n      .on('postgres_changes',\n        { event: '*', schema: 'public', table: 'chat_messages' },\n        (payload) => {\n          console.log('Chat message change received:', payload)\n          if (currentConversation && payload.new && (payload.new as any).conversation_id === currentConversation) {\n            fetchMessages(currentConversation)\n          }\n        }\n      )\n      .subscribe()\n\n    return () => {\n      supabase.removeChannel(messagesSubscription)\n    }\n  }, [])\n\n  useEffect(() => {\n    if (currentConversation) {\n      fetchMessages(currentConversation)\n      setShowSuggestions(false)\n    }\n  }, [currentConversation])\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  const fetchConversations = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('chat_conversations')\n        .select('*')\n        .order('updated_at', { ascending: false })\n\n      if (error) throw error\n      setConversations(data || [])\n      \n      if (data && data.length > 0 && !currentConversation) {\n        setCurrentConversation(data[0].id)\n      }\n    } catch (error) {\n      console.error('Error fetching conversations:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchMessages = async (conversationId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('chat_messages')\n        .select('*')\n        .eq('conversation_id', conversationId)\n        .order('created_at', { ascending: true })\n\n      if (error) throw error\n      setMessages(data || [])\n    } catch (error) {\n      console.error('Error fetching messages:', error)\n    }\n  }\n\n  const createNewConversation = async () => {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { data, error } = await supabase\n        .from('chat_conversations')\n        .insert([\n          {\n            user_id: user.id,\n            title: 'New Conversation',\n          },\n        ])\n        .select()\n        .single()\n\n      if (error) throw error\n\n      setConversations([data, ...conversations])\n      setCurrentConversation(data.id)\n      setMessages([])\n      setShowSuggestions(true)\n      toast.success('New conversation created!')\n    } catch (error) {\n      console.error('Error creating conversation:', error)\n      toast.error('Failed to create conversation. Please try again.')\n    }\n  }\n\n  const deleteConversation = async (conversationId: string) => {\n    if (!confirm('Are you sure you want to delete this conversation?')) return\n\n    try {\n      const { error } = await supabase\n        .from('chat_conversations')\n        .delete()\n        .eq('id', conversationId)\n\n      if (error) throw error\n\n      setConversations(conversations.filter(c => c.id !== conversationId))\n      if (currentConversation === conversationId) {\n        setCurrentConversation(null)\n        setMessages([])\n        setShowSuggestions(true)\n      }\n      toast.success('Conversation deleted successfully!')\n    } catch (error) {\n      console.error('Error deleting conversation:', error)\n      toast.error('Failed to delete conversation. Please try again.')\n    }\n  }\n\n  const updateConversationTitle = async (conversationId: string, newTitle: string) => {\n    try {\n      const { error } = await supabase\n        .from('chat_conversations')\n        .update({ title: newTitle })\n        .eq('id', conversationId)\n\n      if (error) throw error\n\n      setConversations(conversations.map(c =>\n        c.id === conversationId ? { ...c, title: newTitle } : c\n      ))\n      setEditingConversation(null)\n      setEditingTitle('')\n      toast.success('Conversation title updated!')\n    } catch (error) {\n      console.error('Error updating conversation title:', error)\n      toast.error('Failed to update title. Please try again.')\n    }\n  }\n\n  const handleSuggestionClick = (suggestion: string) => {\n    setNewMessage(suggestion)\n    setShowSuggestions(false)\n  }\n\n  const sendMessage = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!newMessage.trim() || !currentConversation || sending) return\n\n    setSending(true)\n    const messageText = newMessage.trim()\n    setNewMessage('')\n    setShowSuggestions(false)\n\n    try {\n      // Add user message to UI immediately\n      const userMessage: ChatMessage = {\n        id: 'temp-' + Date.now(),\n        conversation_id: currentConversation,\n        role: 'user',\n        content: messageText,\n        created_at: new Date().toISOString(),\n      }\n      setMessages(prev => [...prev, userMessage])\n\n      // Add typing indicator\n      const typingMessage: ChatMessage = {\n        id: 'typing-' + Date.now(),\n        conversation_id: currentConversation,\n        role: 'assistant',\n        content: '...',\n        created_at: new Date().toISOString(),\n      }\n      setMessages(prev => [...prev, typingMessage])\n\n      // Send to API\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: messageText,\n          conversationId: currentConversation,\n        }),\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        const errorMessage = errorData.error?.message || errorData.error || 'Failed to send message'\n        throw new Error(errorMessage)\n      }\n\n      const { response: aiResponse } = await response.json()\n\n      // Remove typing indicator and add AI response\n      setMessages(prev => prev.filter(msg => !msg.id.startsWith('typing-')))\n\n      const aiMessage: ChatMessage = {\n        id: 'temp-ai-' + Date.now(),\n        conversation_id: currentConversation,\n        role: 'assistant',\n        content: aiResponse,\n        created_at: new Date().toISOString(),\n      }\n      setMessages(prev => [...prev, aiMessage])\n\n      // Update conversation title if it's the first message\n      if (messages.length === 0) {\n        const title = messageText.length > 30 ? messageText.substring(0, 30) + '...' : messageText\n        updateConversationTitle(currentConversation, title)\n      }\n\n      // Refresh messages from database to get real IDs\n      setTimeout(() => fetchMessages(currentConversation), 500)\n    } catch (error) {\n      console.error('Error sending message:', error)\n      // Remove typing indicator on error\n      setMessages(prev => prev.filter(msg => !msg.id.startsWith('typing-')))\n      toast.error('Failed to send message. Please try again.')\n    } finally {\n      setSending(false)\n    }\n  }\n\n  if (loading) {\n    return <Loading size=\"lg\" text=\"Loading AI Chat...\" className=\"h-64\" />\n  }\n\n  return (\n    <div className=\"h-[calc(100vh-4rem)] flex bg-gray-50 -mx-4 -my-8 sm:-mx-6 lg:-mx-8\">\n      {/* Conversations Sidebar */}\n      <div className=\"w-1/4 bg-white border-r border-gray-200 flex flex-col shadow-sm\">\n        <div className=\"p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50\">\n          <div className=\"flex items-center justify-between mb-3\">\n            <h2 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n              <SparklesIcon className=\"h-5 w-5 mr-2 text-blue-600\" />\n              AI Chat\n            </h2>\n          </div>\n          <Button\n            onClick={createNewConversation}\n            className=\"w-full\"\n            size=\"sm\"\n          >\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            New Chat\n          </Button>\n        </div>\n        <div className=\"flex-1 overflow-y-auto\">\n          {conversations.length === 0 ? (\n            <div className=\"p-6 text-center text-gray-500\">\n              <MessageCircleIcon className=\"mx-auto h-12 w-12 mb-3 text-gray-300\" />\n              <p className=\"text-sm font-medium\">No conversations yet</p>\n              <p className=\"text-xs text-gray-400 mt-1\">Start a new chat to get help with your life management</p>\n            </div>\n          ) : (\n            <div className=\"space-y-1 p-3\">\n              {conversations.map((conversation) => (\n                <motion.div\n                  key={conversation.id}\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  className=\"group relative\"\n                >\n                  <button\n                    onClick={() => setCurrentConversation(conversation.id)}\n                    className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${\n                      currentConversation === conversation.id\n                        ? 'bg-blue-100 text-blue-900 shadow-sm'\n                        : 'hover:bg-gray-50'\n                    }`}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex-1 min-w-0\">\n                        {editingConversation === conversation.id ? (\n                          <input\n                            type=\"text\"\n                            value={editingTitle}\n                            onChange={(e) => setEditingTitle(e.target.value)}\n                            onBlur={() => updateConversationTitle(conversation.id, editingTitle)}\n                            onKeyDown={(e) => {\n                              if (e.key === 'Enter') {\n                                updateConversationTitle(conversation.id, editingTitle)\n                              } else if (e.key === 'Escape') {\n                                setEditingConversation(null)\n                                setEditingTitle('')\n                              }\n                            }}\n                            className=\"w-full text-sm font-medium bg-transparent border-none outline-none\"\n                            autoFocus\n                          />\n                        ) : (\n                          <div className=\"font-medium text-sm truncate\">\n                            {conversation.title}\n                          </div>\n                        )}\n                        <div className=\"text-xs text-gray-500 mt-1\">\n                          {new Date(conversation.updated_at).toLocaleDateString()}\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n                        <button\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            setEditingConversation(conversation.id)\n                            setEditingTitle(conversation.title)\n                          }}\n                          className=\"p-1 hover:bg-gray-200 rounded\"\n                        >\n                          <EditIcon className=\"h-3 w-3 text-gray-500\" />\n                        </button>\n                        <button\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            deleteConversation(conversation.id)\n                          }}\n                          className=\"p-1 hover:bg-red-100 rounded\"\n                        >\n                          <TrashIcon className=\"h-3 w-3 text-red-500\" />\n                        </button>\n                      </div>\n                    </div>\n                  </button>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Chat Area */}\n      <div className=\"flex-1 flex flex-col bg-white\">\n        {currentConversation ? (\n          <>\n            {/* Messages */}\n            <div className=\"flex-1 overflow-y-auto p-6 space-y-6\">\n              {messages.length === 0 && showSuggestions ? (\n                <div className=\"text-center space-y-8\">\n                  <div className=\"text-gray-500\">\n                    <BotIcon className=\"mx-auto h-16 w-16 mb-4 text-blue-500\" />\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Welcome to AI Chat!</h3>\n                    <p className=\"text-gray-600 max-w-md mx-auto\">\n                      I'm your personal AI assistant for LifeManager. I can help you with tasks, budgets, shopping lists, and recipes.\n                    </p>\n                  </div>\n\n                  {/* Quick Suggestions */}\n                  <div className=\"max-w-2xl mx-auto\">\n                    <h4 className=\"text-sm font-medium text-gray-700 mb-4\">Quick suggestions to get started:</h4>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                      {quickSuggestions.map((suggestion, index) => (\n                        <motion.button\n                          key={index}\n                          initial={{ opacity: 0, y: 20 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: index * 0.1 }}\n                          onClick={() => handleSuggestionClick(suggestion.text)}\n                          className=\"p-4 text-left bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors group\"\n                        >\n                          <div className=\"flex items-center space-x-3\">\n                            <suggestion.icon className={`h-5 w-5 ${suggestion.color}`} />\n                            <span className=\"text-sm text-gray-700 group-hover:text-gray-900\">\n                              {suggestion.text}\n                            </span>\n                          </div>\n                        </motion.button>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                <AnimatePresence>\n                  {messages.map((message, index) => (\n                    <motion.div\n                      key={message.id}\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ duration: 0.3 }}\n                      className={`flex ${\n                        message.role === 'user' ? 'justify-end' : 'justify-start'\n                      }`}\n                    >\n                      <div className={`flex items-start space-x-3 max-w-3xl ${\n                        message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''\n                      }`}>\n                        {/* Avatar */}\n                        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${\n                          message.role === 'user'\n                            ? 'bg-blue-600'\n                            : 'bg-gradient-to-br from-purple-500 to-blue-600'\n                        }`}>\n                          {message.role === 'user' ? (\n                            <UserIcon className=\"h-4 w-4 text-white\" />\n                          ) : (\n                            <BotIcon className=\"h-4 w-4 text-white\" />\n                          )}\n                        </div>\n\n                        {/* Message Content */}\n                        <div className={`flex-1 ${\n                          message.role === 'user' ? 'text-right' : 'text-left'\n                        }`}>\n                          <div className={`inline-block px-4 py-3 rounded-2xl shadow-sm ${\n                            message.role === 'user'\n                              ? 'bg-blue-600 text-white'\n                              : message.content === '...'\n                                ? 'bg-gray-100 text-gray-500'\n                                : 'bg-gray-100 text-gray-900'\n                          }`}>\n                            {message.content === '...' ? (\n                              <div className=\"flex space-x-1\">\n                                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                              </div>\n                            ) : (\n                              <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\n                            )}\n                          </div>\n                          <div className={`text-xs text-gray-500 mt-1 ${\n                            message.role === 'user' ? 'text-right' : 'text-left'\n                          }`}>\n                            {new Date(message.created_at).toLocaleTimeString([], {\n                              hour: '2-digit',\n                              minute: '2-digit'\n                            })}\n                          </div>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))}\n                </AnimatePresence>\n              )}\n              <div ref={messagesEndRef} />\n            </div>\n\n            {/* Message Input */}\n            <div className=\"border-t border-gray-200 p-4\">\n              <form onSubmit={sendMessage} className=\"flex space-x-2\">\n                <input\n                  type=\"text\"\n                  value={newMessage}\n                  onChange={(e) => setNewMessage(e.target.value)}\n                  placeholder=\"Type your message...\"\n                  className=\"flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  disabled={sending}\n                />\n                <button\n                  type=\"submit\"\n                  disabled={!newMessage.trim() || sending}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  <SendIcon className=\"h-4 w-4\" />\n                </button>\n              </form>\n            </div>\n          </>\n        ) : (\n          <div className=\"flex-1 flex items-center justify-center\">\n            <div className=\"text-center text-gray-500\">\n              <MessageCircleIcon className=\"mx-auto h-12 w-12 mb-4\" />\n              <h3 className=\"text-lg font-medium\">Select a conversation</h3>\n              <p className=\"text-sm\">Choose a conversation from the sidebar or start a new one</p>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAGA;AACA;AAAA;AACA;;;AAXA;;;;;;;AAee,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,mBAAmB;QACvB;YAAE,MAAM,kOAAA,CAAA,kBAAe;YAAE,MAAM;YAAyC,OAAO;QAAgB;QAC/F;YAAE,MAAM,yNAAA,CAAA,iBAAc;YAAE,MAAM;YAAwC,OAAO;QAAiB;QAC9F;YAAE,MAAM,6NAAA,CAAA,mBAAgB;YAAE,MAAM;YAAiC,OAAO;QAAkB;QAC1F;YAAE,MAAM,yNAAA,CAAA,iBAAc;YAAE,MAAM;YAAmC,OAAO;QAAkB;KAC3F;IAED,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;YAEA,iCAAiC;YACjC,MAAM,uBAAuB,SAC1B,OAAO,CAAC,yBACR,EAAE,CAAC,oBACF;gBAAE,OAAO;gBAAK,QAAQ;gBAAU,OAAO;YAAgB;2DACvD,CAAC;oBACC,QAAQ,GAAG,CAAC,iCAAiC;oBAC7C,IAAI,uBAAuB,QAAQ,GAAG,IAAI,AAAC,QAAQ,GAAG,CAAS,eAAe,KAAK,qBAAqB;wBACtG,cAAc;oBAChB;gBACF;0DAED,SAAS;YAEZ;sCAAO;oBACL,SAAS,aAAa,CAAC;gBACzB;;QACF;6BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,qBAAqB;gBACvB,cAAc;gBACd,mBAAmB;YACrB;QACF;6BAAG;QAAC;KAAoB;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG;QAAC;KAAS;IAEb,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,iBAAiB,QAAQ,EAAE;YAE3B,IAAI,QAAQ,KAAK,MAAM,GAAG,KAAK,CAAC,qBAAqB;gBACnD,uBAAuB,IAAI,CAAC,EAAE,CAAC,EAAE;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,mBAAmB,gBACtB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAK;YAEzC,IAAI,OAAO,MAAM;YACjB,YAAY,QAAQ,EAAE;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC;gBACN;oBACE,SAAS,KAAK,EAAE;oBAChB,OAAO;gBACT;aACD,EACA,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,iBAAiB;gBAAC;mBAAS;aAAc;YACzC,uBAAuB,KAAK,EAAE;YAC9B,YAAY,EAAE;YACd,mBAAmB;YACnB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,QAAQ,uDAAuD;QAEpE,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,sBACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YAEjB,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACpD,IAAI,wBAAwB,gBAAgB;gBAC1C,uBAAuB;gBACvB,YAAY,EAAE;gBACd,mBAAmB;YACrB;YACA,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,0BAA0B,OAAO,gBAAwB;QAC7D,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,sBACL,MAAM,CAAC;gBAAE,OAAO;YAAS,GACzB,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YAEjB,iBAAiB,cAAc,GAAG,CAAC,CAAA,IACjC,EAAE,EAAE,KAAK,iBAAiB;oBAAE,GAAG,CAAC;oBAAE,OAAO;gBAAS,IAAI;YAExD,uBAAuB;YACvB,gBAAgB;YAChB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,cAAc;QACd,mBAAmB;IACrB;IAEA,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,uBAAuB,SAAS;QAE3D,WAAW;QACX,MAAM,cAAc,WAAW,IAAI;QACnC,cAAc;QACd,mBAAmB;QAEnB,IAAI;YACF,qCAAqC;YACrC,MAAM,cAA2B;gBAC/B,IAAI,UAAU,KAAK,GAAG;gBACtB,iBAAiB;gBACjB,MAAM;gBACN,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;YAE1C,uBAAuB;YACvB,MAAM,gBAA6B;gBACjC,IAAI,YAAY,KAAK,GAAG;gBACxB,iBAAiB;gBACjB,MAAM;gBACN,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAc;YAE5C,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,eAAe,UAAU,KAAK,EAAE,WAAW,UAAU,KAAK,IAAI;gBACpE,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,UAAU,UAAU,EAAE,GAAG,MAAM,SAAS,IAAI;YAEpD,8CAA8C;YAC9C,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC;YAE1D,MAAM,YAAyB;gBAC7B,IAAI,aAAa,KAAK,GAAG;gBACzB,iBAAiB;gBACjB,MAAM;gBACN,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;YAExC,sDAAsD;YACtD,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,MAAM,QAAQ,YAAY,MAAM,GAAG,KAAK,YAAY,SAAS,CAAC,GAAG,MAAM,QAAQ;gBAC/E,wBAAwB,qBAAqB;YAC/C;YAEA,iDAAiD;YACjD,WAAW,IAAM,cAAc,sBAAsB;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,mCAAmC;YACnC,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC;YAC1D,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBAAO,6LAAC,sIAAA,CAAA,UAAO;YAAC,MAAK;YAAK,MAAK;YAAqB,WAAU;;;;;;IAChE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,iNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAA+B;;;;;;;;;;;;0CAI3D,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,MAAK;;kDAEL,6LAAC,yMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAIzC,6LAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+NAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;8CAC7B,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;iDAG5C,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;8CAEV,cAAA,6LAAC;wCACC,SAAS,IAAM,uBAAuB,aAAa,EAAE;wCACrD,WAAW,CAAC,4DAA4D,EACtE,wBAAwB,aAAa,EAAE,GACnC,wCACA,oBACJ;kDAEF,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDACZ,wBAAwB,aAAa,EAAE,iBACtC,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/C,QAAQ,IAAM,wBAAwB,aAAa,EAAE,EAAE;4DACvD,WAAW,CAAC;gEACV,IAAI,EAAE,GAAG,KAAK,SAAS;oEACrB,wBAAwB,aAAa,EAAE,EAAE;gEAC3C,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;oEAC7B,uBAAuB;oEACvB,gBAAgB;gEAClB;4DACF;4DACA,WAAU;4DACV,SAAS;;;;;iFAGX,6LAAC;4DAAI,WAAU;sEACZ,aAAa,KAAK;;;;;;sEAGvB,6LAAC;4DAAI,WAAU;sEACZ,IAAI,KAAK,aAAa,UAAU,EAAE,kBAAkB;;;;;;;;;;;;8DAGzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,uBAAuB,aAAa,EAAE;gEACtC,gBAAgB,aAAa,KAAK;4DACpC;4DACA,WAAU;sEAEV,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,6LAAC;4DACC,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,mBAAmB,aAAa,EAAE;4DACpC;4DACA,WAAU;sEAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA3DxB,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;0BAwEhC,6LAAC;gBAAI,WAAU;0BACZ,oCACC;;sCAEE,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,MAAM,KAAK,KAAK,gCACxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAE,WAAU;8DAAiC;;;;;;;;;;;;sDAMhD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DACZ,iBAAiB,GAAG,CAAC,CAAC,YAAY,sBACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4DAEZ,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAG;4DAC7B,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAE;4DAC5B,YAAY;gEAAE,OAAO,QAAQ;4DAAI;4DACjC,SAAS,IAAM,sBAAsB,WAAW,IAAI;4DACpD,WAAU;sEAEV,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,WAAW,IAAI;wEAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,KAAK,EAAE;;;;;;kFACzD,6LAAC;wEAAK,WAAU;kFACb,WAAW,IAAI;;;;;;;;;;;;2DAVf;;;;;;;;;;;;;;;;;;;;;yDAmBf,6LAAC,4LAAA,CAAA,kBAAe;8CACb,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAW,CAAC,KAAK,EACf,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAC1C;sDAEF,cAAA,6LAAC;gDAAI,WAAW,CAAC,qCAAqC,EACpD,QAAQ,IAAI,KAAK,SAAS,qCAAqC,IAC/D;;kEAEA,6LAAC;wDAAI,WAAW,CAAC,oEAAoE,EACnF,QAAQ,IAAI,KAAK,SACb,gBACA,iDACJ;kEACC,QAAQ,IAAI,KAAK,uBAChB,6LAAC,yMAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;iFAEpB,6LAAC,uMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAKvB,6LAAC;wDAAI,WAAW,CAAC,OAAO,EACtB,QAAQ,IAAI,KAAK,SAAS,eAAe,aACzC;;0EACA,6LAAC;gEAAI,WAAW,CAAC,6CAA6C,EAC5D,QAAQ,IAAI,KAAK,SACb,2BACA,QAAQ,OAAO,KAAK,QAClB,8BACA,6BACN;0EACC,QAAQ,OAAO,KAAK,sBACnB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAI,WAAU;4EAAkD,OAAO;gFAAE,gBAAgB;4EAAO;;;;;;sFACjG,6LAAC;4EAAI,WAAU;4EAAkD,OAAO;gFAAE,gBAAgB;4EAAO;;;;;;;;;;;yFAGnG,6LAAC;oEAAE,WAAU;8EAA+B,QAAQ,OAAO;;;;;;;;;;;0EAG/D,6LAAC;gEAAI,WAAW,CAAC,2BAA2B,EAC1C,QAAQ,IAAI,KAAK,SAAS,eAAe,aACzC;0EACC,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB,CAAC,EAAE,EAAE;oEACnD,MAAM;oEACN,QAAQ;gEACV;;;;;;;;;;;;;;;;;;2CAnDD,QAAQ,EAAE;;;;;;;;;;8CA2DvB,6LAAC;oCAAI,KAAK;;;;;;;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,UAAU;gCAAa,WAAU;;kDACrC,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;wCACV,UAAU;;;;;;kDAEZ,6LAAC;wCACC,MAAK;wCACL,UAAU,CAAC,WAAW,IAAI,MAAM;wCAChC,WAAU;kDAEV,cAAA,6LAAC,yMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;iDAM5B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+NAAA,CAAA,oBAAiB;gCAAC,WAAU;;;;;;0CAC7B,6LAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,6LAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC;GA/ewB;KAAA", "debugId": null}}]}