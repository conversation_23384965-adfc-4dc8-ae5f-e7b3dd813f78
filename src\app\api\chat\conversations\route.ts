import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'
import { ensureUserProfile } from '@/lib/auth/profile-manager'

// GET /api/chat/conversations - Get user's conversations
export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: conversations, error } = await supabase
        .from('chat_conversations')
        .select('id, title, created_at, updated_at')
        .eq('user_id', user.id)
        .order('updated_at', { ascending: false })

      if (error) throw error

      const response = NextResponse.json({ conversations: conversations || [] })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// POST /api/chat/conversations - Create new conversation
export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const requestData = await request.json()
      const { title = 'New Conversation' } = requestData
      
      if (typeof title !== 'string' || title.length > 100) {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid title' } 
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: conversation, error } = await supabase
        .from('chat_conversations')
        .insert([
          {
            user_id: user.id,
            title: title.trim(),
          },
        ])
        .select('id, title, created_at, updated_at')
        .single()

      if (error) throw error

      const response = NextResponse.json({ conversation })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
