export interface BudgetCategory {
  id: string
  name: string
  description?: string
  budget_amount: number
  spent_amount: number
  color?: string
  icon?: string
  created_at: string
  updated_at: string
}

export interface Transaction {
  id: string
  category_id: string
  amount: number
  description: string
  date: string
  type: 'income' | 'expense'
  tags?: string[]
  receipt_url?: string
  created_at: string
  updated_at: string
}

export interface BudgetSummary {
  total_budget: number
  total_spent: number
  total_income: number
  remaining_budget: number
  categories: BudgetCategory[]
  recent_transactions: Transaction[]
}

export interface CreateBudgetCategoryData {
  name: string
  description?: string
  budget_amount: number
  color?: string
  icon?: string
}

export interface UpdateBudgetCategoryData extends Partial<CreateBudgetCategoryData> {}

export interface CreateTransactionData {
  category_id: string
  amount: number
  description: string
  date: string
  type: 'income' | 'expense'
  tags?: string[]
  receipt_url?: string
}

export interface UpdateTransactionData extends Partial<CreateTransactionData> {}

class BudgetService {
  private baseUrl = '/api/budget'

  async getBudgetSummary(): Promise<BudgetSummary> {
    const response = await fetch(`${this.baseUrl}/summary`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch budget summary: ${response.statusText}`)
    }

    return response.json()
  }

  async getBudgetCategories(): Promise<BudgetCategory[]> {
    const response = await fetch(`${this.baseUrl}/categories`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch budget categories: ${response.statusText}`)
    }

    return response.json()
  }

  async getBudgetCategory(id: string): Promise<BudgetCategory> {
    const response = await fetch(`${this.baseUrl}/categories/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch budget category: ${response.statusText}`)
    }

    return response.json()
  }

  async createBudgetCategory(data: CreateBudgetCategoryData): Promise<BudgetCategory> {
    const response = await fetch(`${this.baseUrl}/categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to create budget category: ${response.statusText}`)
    }

    return response.json()
  }

  async updateBudgetCategory(id: string, data: UpdateBudgetCategoryData): Promise<BudgetCategory> {
    const response = await fetch(`${this.baseUrl}/categories/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to update budget category: ${response.statusText}`)
    }

    return response.json()
  }

  async deleteBudgetCategory(id: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/categories/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to delete budget category: ${response.statusText}`)
    }
  }

  async getTransactions(categoryId?: string, limit?: number): Promise<Transaction[]> {
    const params = new URLSearchParams()
    if (categoryId) params.append('category_id', categoryId)
    if (limit) params.append('limit', limit.toString())

    const response = await fetch(`${this.baseUrl}/transactions?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch transactions: ${response.statusText}`)
    }

    return response.json()
  }

  async getTransaction(id: string): Promise<Transaction> {
    const response = await fetch(`${this.baseUrl}/transactions/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch transaction: ${response.statusText}`)
    }

    return response.json()
  }

  async createTransaction(data: CreateTransactionData): Promise<Transaction> {
    const response = await fetch(`${this.baseUrl}/transactions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to create transaction: ${response.statusText}`)
    }

    return response.json()
  }

  async updateTransaction(id: string, data: UpdateTransactionData): Promise<Transaction> {
    const response = await fetch(`${this.baseUrl}/transactions/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to update transaction: ${response.statusText}`)
    }

    return response.json()
  }

  async deleteTransaction(id: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/transactions/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to delete transaction: ${response.statusText}`)
    }
  }
}

export const budgetService = new BudgetService()
