import { createClient } from '@/lib/supabase/server'

export interface UserProfile {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  created_at?: string
  updated_at?: string
}

export async function ensureUserProfile(userId: string, email: string, fullName?: string): Promise<UserProfile> {
  const supabase = await createClient()

  try {
    // First, check if profile already exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (existingProfile && !fetchError) {
      return existingProfile
    }

    // If profile doesn't exist, create it
    const { data: newProfile, error: insertError } = await supabase
      .from('profiles')
      .insert([
        {
          id: userId,
          email: email,
          full_name: fullName || email.split('@')[0],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ])
      .select()
      .single()

    if (insertError) {
      console.error('Error creating user profile:', insertError)
      throw new Error('Failed to create user profile')
    }

    return newProfile
  } catch (error) {
    console.error('Error in ensureUserProfile:', error)
    throw error
  }
}

export async function getUserProfile(userId: string): Promise<UserProfile | null> {
  const supabase = await createClient()

  try {
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Error fetching user profile:', error)
      return null
    }

    return profile
  } catch (error) {
    console.error('Error in getUserProfile:', error)
    return null
  }
}

export async function updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile | null> {
  const supabase = await createClient()

  try {
    const { data: updatedProfile, error } = await supabase
      .from('profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating user profile:', error)
      throw new Error('Failed to update user profile')
    }

    return updatedProfile
  } catch (error) {
    console.error('Error in updateUserProfile:', error)
    throw error
  }
}
