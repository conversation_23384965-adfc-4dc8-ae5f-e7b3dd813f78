// Client-side service for chat operations - all calls go through Next.js API

export interface ChatConversation {
  id: string
  title: string
  created_at: string
  updated_at: string
}

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  created_at: string
}

class ChatService {
  private async makeRequest(url: string, options: RequestInit = {}) {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      const errorMessage = errorData.error?.message || errorData.error || 'Request failed'
      throw new Error(errorMessage)
    }

    return response.json()
  }

  // Get all conversations for the current user
  async getConversations(): Promise<ChatConversation[]> {
    const data = await this.makeRequest('/api/chat/conversations')
    return data.conversations
  }

  // Create a new conversation
  async createConversation(title?: string): Promise<ChatConversation> {
    const data = await this.makeRequest('/api/chat/conversations', {
      method: 'POST',
      body: JSON.stringify({ title }),
    })
    return data.conversation
  }

  // Delete a conversation
  async deleteConversation(conversationId: string): Promise<void> {
    await this.makeRequest(`/api/chat/conversations/${conversationId}`, {
      method: 'DELETE',
    })
  }

  // Update conversation title
  async updateConversationTitle(conversationId: string, title: string): Promise<ChatConversation> {
    const data = await this.makeRequest(`/api/chat/conversations/${conversationId}`, {
      method: 'PATCH',
      body: JSON.stringify({ title }),
    })
    return data.conversation
  }

  // Get messages for a conversation
  async getMessages(conversationId: string): Promise<ChatMessage[]> {
    const data = await this.makeRequest(`/api/chat/conversations/${conversationId}/messages`)
    return data.messages
  }

  // Send a message and get AI response
  async sendMessage(message: string, conversationId?: string): Promise<string> {
    const data = await this.makeRequest('/api/chat', {
      method: 'POST',
      body: JSON.stringify({ message, conversationId }),
    })
    return data.response
  }
}

// Export a singleton instance
export const chatService = new ChatService()
