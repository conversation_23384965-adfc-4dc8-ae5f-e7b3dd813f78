export interface ShoppingItem {
  id: string
  name: string
  quantity?: number
  unit?: string
  category?: string
  notes?: string
  completed: boolean
  priority?: 'low' | 'medium' | 'high'
  estimated_price?: number
  actual_price?: number
  created_at: string
  updated_at: string
}

export interface ShoppingList {
  id: string
  name: string
  description?: string
  items: ShoppingItem[]
  total_estimated_cost?: number
  total_actual_cost?: number
  completed: boolean
  created_at: string
  updated_at: string
}

export interface CreateShoppingListData {
  name: string
  description?: string
}

export interface UpdateShoppingListData extends Partial<CreateShoppingListData> {}

export interface CreateShoppingItemData {
  name: string
  quantity?: number
  unit?: string
  category?: string
  notes?: string
  priority?: 'low' | 'medium' | 'high'
  estimated_price?: number
}

export interface UpdateShoppingItemData extends Partial<CreateShoppingItemData> {
  completed?: boolean
  actual_price?: number
}

class ShoppingService {
  private baseUrl = '/api/shopping'

  async getShoppingLists(): Promise<ShoppingList[]> {
    const response = await fetch(this.baseUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch shopping lists: ${response.statusText}`)
    }

    return response.json()
  }

  async getShoppingList(id: string): Promise<ShoppingList> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch shopping list: ${response.statusText}`)
    }

    return response.json()
  }

  async createShoppingList(data: CreateShoppingListData): Promise<ShoppingList> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to create shopping list: ${response.statusText}`)
    }

    return response.json()
  }

  async updateShoppingList(id: string, data: UpdateShoppingListData): Promise<ShoppingList> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to update shopping list: ${response.statusText}`)
    }

    return response.json()
  }

  async deleteShoppingList(id: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to delete shopping list: ${response.statusText}`)
    }
  }

  async addItemToList(listId: string, data: CreateShoppingItemData): Promise<ShoppingItem> {
    const response = await fetch(`${this.baseUrl}/${listId}/items`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to add item to shopping list: ${response.statusText}`)
    }

    return response.json()
  }

  async updateShoppingItem(listId: string, itemId: string, data: UpdateShoppingItemData): Promise<ShoppingItem> {
    const response = await fetch(`${this.baseUrl}/${listId}/items/${itemId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to update shopping item: ${response.statusText}`)
    }

    return response.json()
  }

  async deleteShoppingItem(listId: string, itemId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${listId}/items/${itemId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to delete shopping item: ${response.statusText}`)
    }
  }

  async toggleItemCompleted(listId: string, itemId: string): Promise<ShoppingItem> {
    const response = await fetch(`${this.baseUrl}/${listId}/items/${itemId}/toggle`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to toggle item completion: ${response.statusText}`)
    }

    return response.json()
  }
}

export const shoppingService = new ShoppingService()
