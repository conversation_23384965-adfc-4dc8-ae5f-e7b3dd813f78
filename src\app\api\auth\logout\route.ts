import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { addSecurityHeaders } from '@/lib/security/utils'
import { handleSecureError } from '@/lib/security/error-handler'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    const { error } = await supabase.auth.signOut()

    if (error) {
      return NextResponse.json({
        error: {
          code: 'LOGOUT_FAILED',
          message: 'Failed to logout'
        }
      }, { status: 500 })
    }

    const response = NextResponse.json({
      message: 'Logged out successfully'
    })

    return addSecurityHeaders(response)
  } catch (error) {
    return handleSecureError(error)
  }
}
