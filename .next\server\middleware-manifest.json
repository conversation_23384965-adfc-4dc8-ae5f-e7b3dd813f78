{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3bMorcNST1nn7TlHZNKY9DXdKc+0VsLLH8M8jnlcKQ0=", "__NEXT_PREVIEW_MODE_ID": "ab9cc4bd9c5083f9e10b1412c0f34bd7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a9c7249ce66db4d2db51a655c0d0c20610855f2780d2dc42ec0cf9de392c918", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "229cb18ca52f6c693052c5fee22910eff1032c081e0756898ed9440ba8e95f7c"}}}, "sortedMiddleware": ["/"], "functions": {}}