// Environment variable validation and security checks

interface EnvConfig {
  NEXT_PUBLIC_SUPABASE_URL: string
  NEXT_PUBLIC_SUPABASE_ANON_KEY: string
  SUPABASE_SERVICE_ROLE_KEY?: string
  OPENAI_API_KEY?: string
  SPOONACULAR_API_KEY?: string
}

export function validateEnvironment(): EnvConfig {
  const config: Partial<EnvConfig> = {}

  // Required public variables
  config.NEXT_PUBLIC_SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL
  config.NEXT_PUBLIC_SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!config.NEXT_PUBLIC_SUPABASE_URL) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL is required')
  }

  if (!config.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY is required')
  }

  // Server-only variables (optional but recommended)
  if (typeof window === 'undefined') {
    config.SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY
    config.OPENAI_API_KEY = process.env.OPENAI_API_KEY
    config.SPOONACULAR_API_KEY = process.env.SPOONACULAR_API_KEY

    // Warn if service role key is missing (some features may not work)
    if (!config.SUPABASE_SERVICE_ROLE_KEY) {
      console.warn('SUPABASE_SERVICE_ROLE_KEY is not set - some admin features may not work')
    }

    if (!config.OPENAI_API_KEY) {
      console.warn('OPENAI_API_KEY is not set - AI chat will use fallback responses')
    }
  }

  return config as EnvConfig
}

export function isServerSide(): boolean {
  return typeof window === 'undefined'
}

export function getSecureConfig() {
  const config = validateEnvironment()
  
  // Only return server-side keys when actually on server
  if (isServerSide()) {
    return config
  }
  
  // Client-side: only return public keys
  return {
    NEXT_PUBLIC_SUPABASE_URL: config.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: config.NEXT_PUBLIC_SUPABASE_ANON_KEY
  }
}

// Security check: ensure sensitive keys are not exposed to client
export function checkClientSideSecurity() {
  if (typeof window !== 'undefined') {
    const dangerousKeys = [
      'SUPABASE_SERVICE_ROLE_KEY',
      'OPENAI_API_KEY',
      'SPOONACULAR_API_KEY'
    ]
    
    dangerousKeys.forEach(key => {
      if ((window as any)[key] || (process.env as any)[key]) {
        console.error(`SECURITY WARNING: ${key} is exposed to client-side code!`)
      }
    })
  }
}
