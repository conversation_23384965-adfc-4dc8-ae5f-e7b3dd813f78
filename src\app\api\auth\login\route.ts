import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { addSecurityHeaders } from '@/lib/security/utils'
import { handleSecureError } from '@/lib/security/error-handler'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { rateLimit } from '@/lib/security/rate-limit'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for auth endpoints
    const clientIP = request.ip || 
      request.headers.get('x-forwarded-for') || 
      request.headers.get('x-real-ip') || 
      'unknown'
    
    const rateLimitResult = await rateLimit(
      `auth:${clientIP}`,
      rateLimitConfigs.auth.requests,
      rateLimitConfigs.auth.window
    )
    
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          error: {
            code: 'RATE_LIMIT',
            message: 'Too many login attempts. Please try again later.',
            retryAfter: rateLimitResult.retryAfter
          }
        },
        {
          status: 429,
          headers: {
            'Retry-After': rateLimitResult.retryAfter?.toString() || '900'
          }
        }
      )
    }

    const { email, password } = await request.json()

    // Input validation
    if (!email || !password) {
      return NextResponse.json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Email and password are required'
        }
      }, { status: 400 })
    }

    if (typeof email !== 'string' || typeof password !== 'string') {
      return NextResponse.json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid input format'
        }
      }, { status: 400 })
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid email format'
        }
      }, { status: 400 })
    }

    const supabase = await createClient()

    const { data, error } = await supabase.auth.signInWithPassword({
      email: email.toLowerCase().trim(),
      password,
    })

    if (error) {
      // Log failed login attempt
      console.warn('Failed login attempt:', {
        email: email.toLowerCase().trim(),
        ip: clientIP,
        timestamp: new Date().toISOString(),
        error: error.message
      })

      return NextResponse.json({
        error: {
          code: 'AUTH_FAILED',
          message: 'Invalid email or password'
        }
      }, { status: 401 })
    }

    // Log successful login
    console.log('Successful login:', {
      userId: data.user?.id,
      email: data.user?.email,
      ip: clientIP,
      timestamp: new Date().toISOString()
    })

    const response = NextResponse.json({
      user: {
        id: data.user.id,
        email: data.user.email,
        user_metadata: data.user.user_metadata
      },
      session: {
        access_token: data.session?.access_token,
        refresh_token: data.session?.refresh_token,
        expires_at: data.session?.expires_at
      }
    })

    return addSecurityHeaders(response)
  } catch (error) {
    return handleSecureError(error)
  }
}
