# Security Documentation

## Overview

LifeManager implements comprehensive security measures to protect user data and prevent common vulnerabilities. This document outlines our security architecture and best practices.

## 🔒 Security Architecture

### Authentication & Authorization
- **JWT-based authentication** using Supabase Auth
- **Row Level Security (RLS)** enabled on all tables
- **User isolation** - users can only access their own data
- **Session management** with automatic token refresh
- **Automatic profile creation** for new users
- **Secure error handling** that doesn't expose sensitive information

### API Security
- **Rate limiting** on all API endpoints
- **Input validation** and sanitization
- **SQL injection prevention** through parameterized queries
- **XSS protection** through content sanitization
- **CSRF protection** through SameSite cookies
- **Security headers** on all responses

### Data Protection
- **Environment variable security** - sensitive keys never exposed to client
- **Secure error responses** - no database schema exposure
- **Audit logging** for all sensitive operations
- **Data encryption** at rest and in transit

### RLS Policies
All database tables have comprehensive RLS policies:
- `SELECT`: Users can only view their own data
- `INSERT`: Users can only create records for themselves
- `UPDATE`: Users can only modify their own records
- `DELETE`: Users can only delete their own records

### Shared Resources
For shared shopping lists:
- **Collaboration policies** allow invited users to access shared lists
- **Permission levels** (view, edit, admin) control access
- **Invitation system** with acceptance tracking

## Input Validation & Sanitization

### Server-Side Validation
- **Schema validation** for all API endpoints
- **Type checking** and format validation
- **Length limits** to prevent buffer overflow
- **Enum validation** for restricted values
- **Custom validation** for business logic

### Client-Side Sanitization
- **HTML sanitization** to prevent XSS
- **Input trimming** and normalization
- **File upload restrictions** (if implemented)

### Validation Schemas
```typescript
// Example task validation
{
  title: { required: true, type: 'string', maxLength: 200 },
  description: { required: false, type: 'string', maxLength: 1000 },
  priority: { required: false, type: 'string', enum: ['low', 'medium', 'high'] }
}
```

## Rate Limiting

### Implementation
- **In-memory rate limiting** for development
- **Per-user and per-IP limits**
- **Different limits** for different endpoint types
- **Graceful degradation** with retry-after headers

### Rate Limit Configurations
- **Authentication**: 5 requests per 15 minutes
- **API endpoints**: 100 requests per hour
- **File uploads**: 10 requests per hour
- **Recipe imports**: 20 requests per hour
- **Chat/AI**: 50 requests per hour

## Security Headers

### Implemented Headers
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Content-Security-Policy` (configured for app requirements)

## Audit Logging

### What We Log
- **User actions** (create, update, delete operations)
- **Authentication events**
- **Failed requests** and errors
- **IP addresses** and user agents
- **Timestamps** for all events

### Audit Log Structure
```sql
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  action TEXT NOT NULL,
  table_name TEXT,
  record_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Data Protection

### Encryption
- **Data in transit**: HTTPS/TLS encryption
- **Data at rest**: Supabase handles encryption
- **API keys**: Stored as environment variables
- **Passwords**: Handled by Supabase Auth (bcrypt)

### Data Minimization
- **Collect only necessary data**
- **Regular data cleanup** (if implemented)
- **User data export** capabilities
- **Right to deletion** compliance

## API Security

### Security Middleware
All API routes use security middleware that provides:
- **Authentication verification**
- **Rate limiting**
- **Input validation**
- **Audit logging**
- **Security headers**

### Example Usage
```typescript
export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
    // Route handler
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    validation: { body: commonSchemas.task },
    auditLog: true
  }
)
```

## Database Security

### Connection Security
- **Connection pooling** via Supabase
- **SSL/TLS encryption** for all connections
- **Environment-based configuration**

### Query Security
- **Parameterized queries** (Supabase handles this)
- **No dynamic SQL construction**
- **RLS enforcement** at database level

### Backup & Recovery
- **Automated backups** via Supabase
- **Point-in-time recovery** available
- **Cross-region replication** (if configured)

## Frontend Security

### XSS Prevention
- **React's built-in XSS protection**
- **No `dangerouslySetInnerHTML` usage**
- **Content sanitization** for user inputs
- **CSP headers** to prevent script injection

### CSRF Protection
- **SameSite cookies** (handled by Supabase)
- **Origin validation** for API requests
- **CSRF tokens** (if needed for forms)

### Client-Side Storage
- **No sensitive data** in localStorage
- **Session tokens** handled by Supabase
- **Secure cookie configuration**

## Real-Time Security

### WebSocket Security
- **Authentication required** for real-time subscriptions
- **User-specific channels** only
- **Rate limiting** on subscription events
- **Automatic cleanup** of stale connections

### Presence System
- **User isolation** in presence channels
- **No sensitive data** in presence state
- **Automatic user removal** on disconnect

## Deployment Security

### Environment Variables
```bash
# Required environment variables
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key
OPENAI_API_KEY=your_openai_key
SPOONACULAR_API_KEY=your_spoonacular_key
```

### Production Checklist
- [ ] HTTPS enabled
- [ ] Environment variables secured
- [ ] Database backups configured
- [ ] Monitoring and alerting set up
- [ ] Security headers configured
- [ ] Rate limiting enabled
- [ ] Audit logging active
- [ ] Error handling implemented
- [ ] Dependencies updated

## Monitoring & Incident Response

### Security Monitoring
- **Failed authentication attempts**
- **Rate limit violations**
- **Unusual access patterns**
- **Database query performance**
- **Error rates and patterns**

### Incident Response
1. **Detection** via monitoring alerts
2. **Assessment** of impact and scope
3. **Containment** of the threat
4. **Eradication** of vulnerabilities
5. **Recovery** and system restoration
6. **Lessons learned** and improvements

## Compliance

### Data Privacy
- **GDPR compliance** considerations
- **User consent** for data processing
- **Data portability** features
- **Right to deletion** implementation

### Security Standards
- **OWASP Top 10** mitigation
- **Security by design** principles
- **Regular security assessments**
- **Vulnerability management**

## Security Testing

### Automated Testing
```bash
# Run security audit
node scripts/security-audit.js

# Check for vulnerabilities
npm audit

# Lint for security issues
npm run lint:security
```

### Manual Testing
- **Authentication bypass attempts**
- **Authorization escalation tests**
- **Input validation testing**
- **SQL injection attempts**
- **XSS payload testing**

## Contact

For security issues or questions:
- **Email**: <EMAIL>
- **Bug Bounty**: (if applicable)
- **Responsible Disclosure**: Follow coordinated disclosure

## Updates

This security documentation is updated with each release. Last updated: [Current Date]
