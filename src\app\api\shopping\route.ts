import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'
import { ensureUserProfile } from '@/lib/auth/profile-manager'

// GET /api/shopping - Get user's shopping lists
export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: shoppingLists, error } = await supabase
        .from('shopping_lists')
        .select(`
          id,
          name,
          store_name,
          total_budget,
          estimated_total,
          is_completed,
          created_at,
          updated_at,
          shopping_items (
            id,
            name,
            quantity,
            unit,
            estimated_price,
            actual_price,
            is_purchased,
            category,
            notes
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error

      const response = NextResponse.json({ shoppingLists: shoppingLists || [] })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// POST /api/shopping - Create new shopping list
export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const requestData = await request.json()
      const {
        name,
        store_name,
        total_budget
      } = requestData
      
      // Basic validation
      if (!name || typeof name !== 'string' || name.length > 100) {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid name' } 
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: shoppingList, error } = await supabase
        .from('shopping_lists')
        .insert([
          {
            user_id: user.id,
            name: name.trim(),
            store_name: store_name || null,
            total_budget: total_budget || null,
            estimated_total: 0,
            is_completed: false
          },
        ])
        .select()
        .single()

      if (error) throw error

      const response = NextResponse.json({ shoppingList })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
