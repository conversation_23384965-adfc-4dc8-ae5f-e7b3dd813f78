'use client'

import { useState, useEffect, useMemo } from 'react'
import { recipesService, type Recipe } from '@/lib/services/recipes-service'
import { motion, AnimatePresence } from 'framer-motion'
import { PlusIcon, LinkIcon, ChefHatIcon, CalendarIcon, BarChart3Icon, GridIcon, ListIcon } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Modal } from '@/components/ui/Modal'
import EnhancedRecipeCard from '@/components/recipes/EnhancedRecipeCard'
import DietaryFilters, { RecipeFilters } from '@/components/recipes/DietaryFilters'
import MealPlanModal from '@/components/recipes/MealPlanModal'
import toast from 'react-hot-toast'

export default function RecipesPage() {
  const [recipes, setRecipes] = useState<Recipe[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [showImportForm, setShowImportForm] = useState(false)
  const [showMealPlanModal, setShowMealPlanModal] = useState(false)
  const [selectedRecipeForMealPlan, setSelectedRecipeForMealPlan] = useState<Recipe | null>(null)
  const [importUrl, setImportUrl] = useState('')
  const [importing, setImporting] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [filters, setFilters] = useState<RecipeFilters>({
    search: '',
    difficulty: [],
    cuisine: [],
    dietaryRestrictions: [],
    maxPrepTime: null,
    maxCookTime: null,
    minServings: null,
    maxServings: null,
    tags: []
  })
  const [newRecipe, setNewRecipe] = useState({
    title: '',
    description: '',
    prep_time: '',
    cook_time: '',
    servings: '',
    difficulty: 'medium' as 'easy' | 'medium' | 'hard',
    cuisine: '',
    ingredients: [''],
    instructions: [''],
  })

  useEffect(() => {
    fetchRecipes()

    // Set up polling for real-time updates (secure alternative to direct Supabase subscriptions)
    const pollInterval = setInterval(() => {
      fetchRecipes()
    }, 5000) // Poll every 5 seconds

    return () => {
      clearInterval(pollInterval)
    }
  }, [])

  // Filter recipes based on current filters
  const filteredRecipes = useMemo(() => {
    return recipes.filter(recipe => {
      // Search filter
      if (filters.search && !recipe.title.toLowerCase().includes(filters.search.toLowerCase()) &&
          !recipe.description?.toLowerCase().includes(filters.search.toLowerCase())) {
        return false
      }

      // Difficulty filter
      if (filters.difficulty.length > 0 && !filters.difficulty.includes(recipe.difficulty)) {
        return false
      }

      // Cuisine filter
      if (filters.cuisine.length > 0 && (!recipe.cuisine || !filters.cuisine.includes(recipe.cuisine))) {
        return false
      }

      // Time filters
      if (filters.maxPrepTime && recipe.prep_time && recipe.prep_time > filters.maxPrepTime) {
        return false
      }
      if (filters.maxCookTime && recipe.cook_time && recipe.cook_time > filters.maxCookTime) {
        return false
      }

      // Servings filters
      if (filters.minServings && recipe.servings && recipe.servings < filters.minServings) {
        return false
      }
      if (filters.maxServings && recipe.servings && recipe.servings > filters.maxServings) {
        return false
      }

      // Dietary restrictions filter (check tags)
      if (filters.dietaryRestrictions.length > 0) {
        const recipeTags = recipe.tags || []
        const hasMatchingDiet = filters.dietaryRestrictions.some(diet => 
          recipeTags.some(tag => tag.toLowerCase().includes(diet.toLowerCase()))
        )
        if (!hasMatchingDiet) return false
      }

      return true
    })
  }, [recipes, filters])

  const fetchRecipes = async () => {
    try {
      const data = await recipesService.getRecipes()
      setRecipes(data || [])
    } catch (error) {
      console.error('Error fetching recipes:', error)
      toast.error('Failed to load recipes')
    } finally {
      setLoading(false)
    }
  }

  const handleAddToMealPlan = (recipeId: string) => {
    const recipe = recipes.find(r => r.id === recipeId)
    if (recipe) {
      setSelectedRecipeForMealPlan(recipe)
      setShowMealPlanModal(true)
    }
  }

  const handleMealPlanSubmit = async (mealPlanData: any) => {
    if (!selectedRecipeForMealPlan) return

    try {
      const response = await fetch('/api/meal-plans', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          recipeId: selectedRecipeForMealPlan.id,
          ...mealPlanData
        })
      })

      if (!response.ok) throw new Error('Failed to add to meal plan')

      toast.success('Added to meal plan!')
      setShowMealPlanModal(false)
      setSelectedRecipeForMealPlan(null)
    } catch (error) {
      console.error('Error adding to meal plan:', error)
      toast.error('Failed to add to meal plan')
    }
  }

  const handleAddToShoppingList = async (recipeId: string) => {
    try {
      const response = await fetch(`/api/recipes/${recipeId}/add-to-shopping-list`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ servings: 1 })
      })

      if (!response.ok) throw new Error('Failed to add to shopping list')

      const result = await response.json()
      toast.success(`Added ${result.addedItems} ingredients to shopping list!`)
    } catch (error) {
      console.error('Error adding to shopping list:', error)
      toast.error('Failed to add ingredients to shopping list')
    }
  }

  const handleAnalyzeNutrition = async (recipeId: string) => {
    try {
      const response = await fetch(`/api/recipes/${recipeId}/nutrition`, {
        method: 'POST'
      })

      if (!response.ok) throw new Error('Failed to analyze nutrition')

      // Refresh recipes to show updated nutritional info
      await fetchRecipes()
      toast.success('Nutritional analysis completed!')
    } catch (error) {
      console.error('Error analyzing nutrition:', error)
      toast.error('Failed to analyze nutrition')
    }
  }

  const importRecipe = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!importUrl.trim()) return

    setImporting(true)
    try {
      const recipe = await recipesService.importRecipeFromUrl(importUrl)

      setImportUrl('')
      setShowImportForm(false)
      toast.success('Recipe imported successfully!')
      fetchRecipes()
    } catch (error) {
      console.error('Error importing recipe:', error)
      toast.error('Failed to import recipe. Please check the URL and try again.')
    } finally {
      setImporting(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Recipe Collection</h1>
          <p className="mt-2 text-gray-600">
            Discover, save, and organize your favorite recipes with advanced features
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {/* View Mode Toggle */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="h-8"
            >
              <GridIcon className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="h-8"
            >
              <ListIcon className="h-4 w-4" />
            </Button>
          </div>

          <Button
            onClick={() => setShowImportForm(true)}
            variant="outline"
          >
            <LinkIcon className="h-4 w-4 mr-2" />
            Import Recipe
          </Button>
          <Button onClick={() => setShowAddForm(true)}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Recipe
          </Button>
        </div>
      </motion.div>

      {/* Filters */}
      <DietaryFilters
        onFiltersChange={setFilters}
        totalRecipes={recipes.length}
        filteredCount={filteredRecipes.length}
      />

      {/* Import Recipe Form */}
      <Modal isOpen={showImportForm} onClose={() => setShowImportForm(false)} size="md">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Import Recipe from URL</h3>
          <form onSubmit={importRecipe} className="space-y-4">
            <div>
              <label htmlFor="importUrl" className="block text-sm font-medium text-gray-700 mb-1">
                Recipe URL
              </label>
              <input
                type="url"
                id="importUrl"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={importUrl}
                onChange={(e) => setImportUrl(e.target.value)}
                placeholder="https://example.com/recipe"
              />
            </div>
            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowImportForm(false)}
                disabled={importing}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={importing}>
                {importing ? 'Importing...' : 'Import Recipe'}
              </Button>
            </div>
          </form>
        </div>
      </Modal>

      {/* Recipes Grid/List */}
      <AnimatePresence mode="wait">
        {filteredRecipes.length === 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <ChefHatIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              {recipes.length === 0 ? 'No recipes yet' : 'No recipes match your filters'}
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {recipes.length === 0 
                ? 'Get started by adding your first recipe or importing from a URL.'
                : 'Try adjusting your search criteria or filters.'
              }
            </p>
          </motion.div>
        ) : (
          <motion.div
            key={viewMode}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className={viewMode === 'grid' 
              ? 'grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'space-y-4'
            }
          >
            {filteredRecipes.map((recipe, index) => (
              <motion.div
                key={recipe.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <EnhancedRecipeCard
                  recipe={recipe}
                  onAddToMealPlan={handleAddToMealPlan}
                  onAddToShoppingList={handleAddToShoppingList}
                  onAnalyzeNutrition={handleAnalyzeNutrition}
                />
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Meal Plan Modal */}
      {selectedRecipeForMealPlan && (
        <MealPlanModal
          isOpen={showMealPlanModal}
          onClose={() => {
            setShowMealPlanModal(false)
            setSelectedRecipeForMealPlan(null)
          }}
          onSubmit={handleMealPlanSubmit}
          recipeTitle={selectedRecipeForMealPlan.title}
          defaultServings={selectedRecipeForMealPlan.servings || 1}
        />
      )}
    </div>
  )
}
