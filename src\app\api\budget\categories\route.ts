import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'
import { ensureUserProfile } from '@/lib/auth/profile-manager'

// GET /api/budget/categories - Get user's budget categories
export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: categories, error } = await supabase
        .from('budget_categories')
        .select(`
          id,
          name,
          type,
          budget_limit,
          color,
          icon,
          created_at,
          updated_at
        `)
        .eq('user_id', user.id)
        .order('name', { ascending: true })

      if (error) throw error

      const response = NextResponse.json({ categories: categories || [] })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// POST /api/budget/categories - Create new budget category
export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const requestData = await request.json()
      const { name, type, budget_limit, color, icon } = requestData
      
      // Basic validation
      if (!name || typeof name !== 'string' || name.length > 100) {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid name' } 
        }, { status: 400 })
      }

      if (!type || !['income', 'expense'].includes(type)) {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Type must be income or expense' } 
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: category, error } = await supabase
        .from('budget_categories')
        .insert([
          {
            user_id: user.id,
            name: name.trim(),
            type,
            budget_limit: budget_limit || null,
            color: color || null,
            icon: icon || null
          },
        ])
        .select()
        .single()

      if (error) throw error

      const response = NextResponse.json({ category })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
