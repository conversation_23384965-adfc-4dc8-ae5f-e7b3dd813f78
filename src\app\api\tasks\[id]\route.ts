import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'

// PATCH /api/tasks/[id] - Update task
export const PATCH = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const { params } = context
      const taskId = params.id
      const requestData = await request.json()

      if (!taskId || typeof taskId !== 'string') {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid task ID' } 
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // First verify the task belongs to the user
      const { data: existingTask, error: fetchError } = await supabase
        .from('tasks')
        .select('id')
        .eq('id', taskId)
        .eq('user_id', user.id)
        .single()

      if (fetchError || !existingTask) {
        return NextResponse.json({ 
          error: { code: 'NOT_FOUND', message: 'Task not found' } 
        }, { status: 404 })
      }

      // Update the task
      const { data: task, error } = await supabase
        .from('tasks')
        .update(requestData)
        .eq('id', taskId)
        .eq('user_id', user.id)
        .select()
        .single()

      if (error) throw error

      const response = NextResponse.json({ task })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// DELETE /api/tasks/[id] - Delete task
export const DELETE = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const { params } = context
      const taskId = params.id

      if (!taskId || typeof taskId !== 'string') {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid task ID' } 
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // Delete the task
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId)
        .eq('user_id', user.id)

      if (error) throw error

      const response = NextResponse.json({ success: true })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
